#!/usr/bin/env python3
"""
修复实验问题的脚本
"""

import os
import sys
import json
import shutil
import subprocess
from pathlib import Path

def fix_gas_price_issue():
    """修复Gas价格过低的问题"""
    print("🔧 修复Gas价格设置...")

    # 修复连接管理器中的Gas价格
    connection_manager_path = "chainfl/connection_manager.py"
    if os.path.exists(connection_manager_path):
        with open(connection_manager_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 增加Gas价格 - 更激进的调整
        content = content.replace(
            "'gas_price': 25000000000,",  # 25 gwei
            "'gas_price': 100000000000,"  # 100 gwei
        )
        content = content.replace(
            "25000000000",  # 任何25 gwei的设置
            "100000000000"  # 100 gwei
        )

        # 增加Gas限制
        content = content.replace(
            "'gas_limit': 8000000,",
            "'gas_limit': 15000000,"
        )
        content = content.replace(
            "8000000",  # 任何8M的gas limit
            "15000000"  # 15M gas limit
        )

        with open(connection_manager_path, 'w', encoding='utf-8') as f:
            f.write(content)

        print("   ✅ Gas价格已调整为100 gwei，Gas限制调整为15M")

    # 修复PoL区块链客户端的Gas设置
    pol_client_path = "chainfl/pol_blockchain_client.py"
    if os.path.exists(pol_client_path):
        with open(pol_client_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 增加Gas价格和限制
        content = content.replace(
            "gas_price=25000000000",
            "gas_price=100000000000"
        )
        content = content.replace(
            "gas_limit=8000000",
            "gas_limit=15000000"
        )

        with open(pol_client_path, 'w', encoding='utf-8') as f:
            f.write(content)

        print("   ✅ PoL客户端Gas设置已更新")

def fix_timeout_issues():
    """修复超时问题"""
    print("🔧 修复超时设置...")
    
    # 修复并行执行器的超时设置
    parallel_executor_path = "experiments/parallel_executor.py"
    if os.path.exists(parallel_executor_path):
        with open(parallel_executor_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 增加默认超时时间
        content = content.replace(
            "timeout: int = 1800,",  # 30分钟
            "timeout: int = 7200,"   # 2小时
        )
        
        # 增加网络超时设置
        if "requests.post" in content and "timeout=" not in content:
            content = content.replace(
                "requests.post(",
                "requests.post(timeout=60, "
            )
        
        with open(parallel_executor_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("   ✅ 实验超时时间已调整为2小时")

def fix_data_download_issues():
    """修复数据下载问题"""
    print("🔧 修复数据下载问题...")
    
    # 预下载数据集
    data_dir = Path("experiments/data")
    data_dir.mkdir(parents=True, exist_ok=True)
    
    print("   📥 预下载FashionMNIST数据集...")
    try:
        import torch
        import torchvision
        from torchvision import datasets, transforms
        
        # 下载FashionMNIST
        transform = transforms.Compose([transforms.ToTensor()])
        
        train_dataset = datasets.FashionMNIST(
            root=str(data_dir),
            train=True,
            download=True,
            transform=transform
        )
        
        test_dataset = datasets.FashionMNIST(
            root=str(data_dir),
            train=False,
            download=True,
            transform=transform
        )
        
        print("   ✅ FashionMNIST数据集下载完成")
        
        # 下载CIFAR10
        train_dataset = datasets.CIFAR10(
            root=str(data_dir),
            train=True,
            download=True,
            transform=transform
        )
        
        test_dataset = datasets.CIFAR10(
            root=str(data_dir),
            train=False,
            download=True,
            transform=transform
        )
        
        print("   ✅ CIFAR10数据集下载完成")
        
        # 下载CIFAR100
        train_dataset = datasets.CIFAR100(
            root=str(data_dir),
            train=True,
            download=True,
            transform=transform
        )
        
        test_dataset = datasets.CIFAR100(
            root=str(data_dir),
            train=False,
            download=True,
            transform=transform
        )
        
        print("   ✅ CIFAR100数据集下载完成")
        
    except Exception as e:
        print(f"   ⚠️ 数据集下载失败: {e}")
        print("   💡 建议手动下载数据集或检查网络连接")

def fix_blockchain_config():
    """修复区块链配置"""
    print("🔧 修复区块链配置...")
    
    # 创建更稳定的区块链配置
    brownie_config_path = "chainEnv/brownie-config.yaml"
    if os.path.exists(brownie_config_path):
        config_content = """
dependencies:
  - OpenZeppelin/openzeppelin-contracts@4.8.0

compiler:
  solc:
    version: 0.8.19
    optimizer:
      enabled: true
      runs: 200

networks:
  development:
    host: http://127.0.0.1:8545
    gas_limit: 12000000
    gas_price: 50000000000
    timeout: 120
    max_fee: 100000000000
    priority_fee: 2000000000

dotenv: .env
"""
        
        with open(brownie_config_path, 'w', encoding='utf-8') as f:
            f.write(config_content.strip())
        
        print("   ✅ Brownie配置已更新")

def create_optimized_experiment_config():
    """创建优化的实验配置"""
    print("🔧 创建优化的实验配置...")
    
    config_content = """#!/usr/bin/env python3
\"\"\"
优化的实验配置 - 修复了超时和Gas问题
\"\"\"

from experiments.parallel_executor import ExperimentConfig

# 基础性能对比实验 (更短的轮次)
BASIC_PERFORMANCE_EXPERIMENTS = [
    ExperimentConfig(
        name="baseline_fashion_mnist_short",
        benchmark="FashionMNIST",
        communication_rounds=2,  # 减少轮次
        enable_pol=False,
        client_num=5,  # 减少客户端数量
        timeout=3600,  # 1小时
    ),
    ExperimentConfig(
        name="pol_fashion_mnist_short",
        benchmark="PoLFashionMNIST",
        communication_rounds=2,
        enable_pol=True,
        client_num=5,
        timeout=3600,
    ),
]

# PoL功能验证实验
POL_VERIFICATION_EXPERIMENTS = [
    ExperimentConfig(
        name="pol_basic_test",
        benchmark="PoLFashionMNIST",
        communication_rounds=1,
        enable_pol=True,
        client_num=3,
        timeout=1800,
    ),
]

# 攻击检测实验 (简化版)
ATTACK_DETECTION_EXPERIMENTS = [
    ExperimentConfig(
        name="attack_free_rider_simple",
        benchmark="PoLFashionMNIST",
        communication_rounds=2,
        enable_pol=True,
        client_num=5,
        attack_type="free_rider",
        malicious_ratio=0.2,
        timeout=3600,
    ),
]

# 实验套件定义
EXPERIMENT_SUITES = {
    "quick": POL_VERIFICATION_EXPERIMENTS,
    "basic": BASIC_PERFORMANCE_EXPERIMENTS,
    "attack": ATTACK_DETECTION_EXPERIMENTS,
    "all": BASIC_PERFORMANCE_EXPERIMENTS + POL_VERIFICATION_EXPERIMENTS + ATTACK_DETECTION_EXPERIMENTS,
}
"""
    
    with open("experiments/optimized_config.py", 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("   ✅ 优化的实验配置已创建")

def create_quick_test_script():
    """创建快速测试脚本"""
    print("🔧 创建快速测试脚本...")
    
    script_content = """#!/usr/bin/env python3
\"\"\"
快速测试脚本 - 验证修复效果
\"\"\"

import sys
import os
sys.path.append('.')

from experiments.parallel_executor import ParallelExecutor, ExperimentConfig

def test_blockchain_connection():
    \"\"\"测试区块链连接\"\"\"
    print("🔍 测试区块链连接...")
    try:
        import requests
        response = requests.get("http://localhost:8545", timeout=5)
        print(f"   ✅ 区块链连接正常: {response.status_code}")
        return True
    except Exception as e:
        print(f"   ❌ 区块链连接失败: {e}")
        return False

def test_data_availability():
    \"\"\"测试数据集可用性\"\"\"
    print("🔍 测试数据集可用性...")
    try:
        import torch
        import torchvision
        from torchvision import datasets, transforms
        
        transform = transforms.Compose([transforms.ToTensor()])
        
        # 测试FashionMNIST
        dataset = datasets.FashionMNIST(
            root="./experiments/data",
            train=True,
            download=False,  # 不下载，只检查
            transform=transform
        )
        print(f"   ✅ FashionMNIST可用: {len(dataset)} 样本")
        return True
    except Exception as e:
        print(f"   ⚠️ 数据集问题: {e}")
        return False

def run_quick_test():
    \"\"\"运行快速测试\"\"\"
    print("🚀 开始快速测试...")
    
    # 检查前置条件
    if not test_blockchain_connection():
        print("❌ 请先启动Ganache区块链")
        return False
    
    test_data_availability()
    
    # 运行简单的PoL测试
    print("🔍 运行简单的PoL测试...")
    
    config = ExperimentConfig(
        name="quick_pol_test",
        benchmark="PoLFashionMNIST",
        communication_rounds=1,
        enable_pol=True,
        client_num=2,
        timeout=1800,
    )
    
    executor = ParallelExecutor(max_parallel=1)
    
    try:
        results = executor.run_experiments([config])
        
        if results and len(results) > 0 and results[0].get('success', False):
            print("✅ 快速测试通过！")
            return True
        else:
            print("❌ 快速测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

if __name__ == "__main__":
    success = run_quick_test()
    sys.exit(0 if success else 1)
"""
    
    with open("experiments/quick_test.py", 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    os.chmod("experiments/quick_test.py", 0o755)
    print("   ✅ 快速测试脚本已创建")

def diagnose_experiment_issues():
    """诊断实验问题"""
    print("🔍 诊断实验问题...")
    print("=" * 50)

    issues_found = []

    # 检查实验报告
    report_path = "experiments/experiments/master_experiment_results_20250729_222536/parallel_results/experiment_report.json"
    if os.path.exists(report_path):
        with open(report_path, 'r', encoding='utf-8') as f:
            report = json.load(f)

        print(f"📊 实验统计:")
        print(f"   总实验数: {report['summary']['total_experiments']}")
        print(f"   成功实验: {report['summary']['successful_experiments']}")
        print(f"   失败实验: {report['summary']['failed_experiments']}")
        print(f"   成功率: {report['summary']['success_rate']:.1%}")

        # 分析失败原因
        failure_reasons = {}
        for exp in report['experiments']:
            if not exp['success']:
                reason = exp.get('error_message', 'Unknown')
                failure_reasons[reason] = failure_reasons.get(reason, 0) + 1

        print(f"\n❌ 失败原因分析:")
        for reason, count in failure_reasons.items():
            print(f"   • {reason}: {count} 次")

            if "transaction underpriced" in reason.lower():
                issues_found.append("gas_price")
            elif "timeout" in reason.lower():
                issues_found.append("timeout")
            elif "exit code" in reason.lower():
                issues_found.append("runtime_error")

    # 检查日志中的具体问题
    log_files = []
    results_dir = "experiments/experiments/master_experiment_results_20250729_222536/parallel_results"
    if os.path.exists(results_dir):
        for item in os.listdir(results_dir):
            log_path = os.path.join(results_dir, item, "experiment.log")
            if os.path.exists(log_path):
                log_files.append(log_path)

    print(f"\n🔍 分析 {len(log_files)} 个实验日志...")

    gas_issues = 0
    download_issues = 0
    timeout_issues = 0

    for log_path in log_files[:3]:  # 只检查前3个避免输出过多
        try:
            with open(log_path, 'r', encoding='utf-8') as f:
                log_content = f.read()

            if "transaction underpriced" in log_content:
                gas_issues += 1
            if "Downloading http://fashion-mnist" in log_content:
                download_issues += 1
            if "socket.timeout" in log_content:
                timeout_issues += 1

        except Exception as e:
            print(f"   ⚠️ 无法读取日志 {log_path}: {e}")

    print(f"\n📋 问题统计:")
    print(f"   Gas价格问题: {gas_issues} 个实验")
    print(f"   数据下载问题: {download_issues} 个实验")
    print(f"   网络超时问题: {timeout_issues} 个实验")

    return issues_found

def main():
    """主修复函数"""
    print("🔧 开始修复实验问题...")
    print("=" * 50)

    # 先诊断问题
    issues = diagnose_experiment_issues()
    print("\n" + "=" * 50)
    
    # 检查当前目录
    if not os.path.exists("chainfl") or not os.path.exists("experiments"):
        print("❌ 请在VeryFL-PoL-main目录下运行此脚本")
        return 1
    
    try:
        # 执行修复
        fix_gas_price_issue()
        fix_timeout_issues()
        fix_blockchain_config()
        create_optimized_experiment_config()
        create_quick_test_script()
        
        # 可选：预下载数据集
        print("\n📥 是否预下载数据集？(可能需要较长时间)")
        response = input("输入 y/yes 继续，其他键跳过: ").lower()
        if response in ['y', 'yes']:
            fix_data_download_issues()
        else:
            print("   ⏭️ 跳过数据集下载")
        
        print("\n" + "=" * 50)
        print("✅ 修复完成！")
        print("\n📋 修复内容:")
        print("   • Gas价格调整为50 gwei")
        print("   • 实验超时时间调整为2小时")
        print("   • 优化了区块链配置")
        print("   • 创建了优化的实验配置")
        print("   • 创建了快速测试脚本")
        
        print("\n🚀 建议的下一步:")
        print("   1. 重启Ganache区块链")
        print("   2. 运行快速测试: python experiments/quick_test.py")
        print("   3. 运行优化实验: python experiments/master_experiment_runner.py --experiment quick")
        
        return 0
        
    except Exception as e:
        print(f"❌ 修复过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
