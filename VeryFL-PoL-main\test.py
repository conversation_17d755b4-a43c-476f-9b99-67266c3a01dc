import logging
import argparse

from task import Task
import config.benchmark
from config.log import set_log_config
logger = logging.getLogger(__name__)
set_log_config()

#global_args, train_args = config.benchmark.FashionMNIST().get_args()
#global_args, train_args, algorithm = config.benchmark.Sign().get_args()

if __name__=="__main__":
    parser = argparse.ArgumentParser(description="VeryFL-PoL 实验执行器")
    parser.add_argument('--benchmark', type=str, default="FashionMNIST",
                       help="运行的基准测试 (参见 ./config/benchmark.py)")
    parser.add_argument('--communication-rounds', type=int, default=None,
                       help="通信轮数")
    parser.add_argument('--enable-pol', action='store_true',
                       help="启用PoL验证")
    parser.add_argument('--client-num', type=int, default=None,
                       help="客户端数量")
    parser.add_argument('--attack-type', type=str, default=None,
                       help="攻击类型 (free_rider, partial_free_rider, data_poison, model_poison)")
    parser.add_argument('--malicious-ratio', type=float, default=0.0,
                       help="恶意客户端比例 (0.0-1.0)")
    parser.add_argument('--compression-method', type=str, default=None,
                       help="压缩方法 (none, quantization, incremental)")
    parser.add_argument('--network-condition', type=str, default=None,
                       help="网络条件 (ideal, high_latency, low_bandwidth, unstable)")

    args = parser.parse_args()
    logger.info(f"获取基准测试: {args.benchmark}")

    # 获取基准配置
    benchmark = config.benchmark.get_benchmark(args.benchmark)
    global_args, train_args, algorithm = benchmark.get_args()

    # 应用命令行参数覆盖
    if args.communication_rounds is not None:
        global_args['communication_round'] = args.communication_rounds
        logger.info(f"设置通信轮数: {args.communication_rounds}")

    if args.enable_pol:
        global_args['enable_pol'] = True
        logger.info("启用PoL验证")

    if args.client_num is not None:
        global_args['client_num'] = args.client_num
        logger.info(f"设置客户端数量: {args.client_num}")

    if args.attack_type is not None:
        global_args['attack_type'] = args.attack_type
        global_args['malicious_ratio'] = args.malicious_ratio
        logger.info(f"设置攻击类型: {args.attack_type}, 恶意比例: {args.malicious_ratio}")

    if args.compression_method is not None:
        global_args['compression_method'] = args.compression_method
        logger.info(f"设置压缩方法: {args.compression_method}")

    if args.network_condition is not None:
        global_args['network_condition'] = args.network_condition
        logger.info(f"设置网络条件: {args.network_condition}")

    logger.info("--训练开始--")
    logger.info("获取全局参数 - 数据集: %s, 模型: %s", global_args['dataset'], global_args['model'])
    classification_task = Task(global_args=global_args, train_args=train_args, algorithm=algorithm)
    classification_task.run()

