# VeryFL-PoL 生成的文件 - 统一放在experiments目录下
experiments/data/
experiments/log/
experiments/pol_data/
experiments/results/
experiments/models/
experiments/monitor*/
experiments/parallel_results*/
experiments/sota_results*/
experiments/compression_results*/
experiments/network_results*/
experiments/ablation_results*/
experiments/temp/

# 但保留实验脚本和README
!experiments/*.py
!experiments/*.md
!experiments/__pycache__/

# 其他临时文件
*.tmp
*.temp
*.bak
*.lock
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
# 自动添加的临时文件忽略规则
# 临时和测试文件
*_test.py
test_*.py
*_tmp.py
tmp_*.py
*_temp.py
temp_*.py
*.tmp
*.temp
*.bak
debug_*.py
cleanup_*.py

# GPU测试文件
gpu_test.py

# 修复脚本（完成使命后删除）
fix_*.py
