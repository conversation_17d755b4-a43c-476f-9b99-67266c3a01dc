#!/usr/bin/env python3
"""
实验框架基础类
提供统一的实验执行、配置管理和结果收集接口
"""

import os
import sys
import json
import time
import logging
import subprocess
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

@dataclass
class ExperimentConfig:
    """实验配置类"""
    name: str
    benchmark: str
    communication_rounds: int = 5
    enable_pol: bool = True
    client_num: int = 10
    attack_type: Optional[str] = None
    malicious_ratio: float = 0.0
    compression_method: Optional[str] = None
    network_condition: Optional[str] = None
    timeout: int = 1800  # 30分钟超时
    
    def to_command_args(self) -> List[str]:
        """转换为命令行参数"""
        cmd = [
            "python", "test.py",
            "--benchmark", self.benchmark,
            "--communication-rounds", str(self.communication_rounds)
        ]
        
        if self.enable_pol:
            cmd.append("--enable-pol")
        
        if self.client_num != 10:  # 默认值
            cmd.extend(["--client-num", str(self.client_num)])
        
        if self.attack_type:
            cmd.extend(["--attack-type", self.attack_type])
            cmd.extend(["--malicious-ratio", str(self.malicious_ratio)])
        
        if self.compression_method:
            cmd.extend(["--compression-method", self.compression_method])
        
        if self.network_condition:
            cmd.extend(["--network-condition", self.network_condition])
        
        return cmd

@dataclass
class ExperimentResult:
    """实验结果类"""
    config: ExperimentConfig
    success: bool
    start_time: str
    end_time: str
    duration: float
    final_accuracy: float = 0.0
    convergence_rounds: int = 0
    total_time: float = 0.0
    communication_cost: float = 0.0
    storage_cost: float = 0.0
    error_message: Optional[str] = None
    log_file: Optional[str] = None
    metrics: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metrics is None:
            self.metrics = {}

class ExperimentFramework:
    """实验框架主类"""
    
    def __init__(self, output_dir: str = "./experiment_results"):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(f'{output_dir}/experiment_framework.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def run_single_experiment(self, config: ExperimentConfig) -> Optional[ExperimentResult]:
        """运行单个实验"""
        self.logger.info(f"开始实验: {config.name}")
        
        # 创建实验目录
        exp_dir = os.path.join(self.output_dir, config.name)
        os.makedirs(exp_dir, exist_ok=True)
        
        # 准备命令
        cmd = config.to_command_args()
        log_file = os.path.join(exp_dir, "experiment.log")
        
        start_time = datetime.now()
        start_time_str = start_time.isoformat()
        
        try:
            # 运行实验
            with open(log_file, 'w') as f:
                process = subprocess.Popen(
                    cmd,
                    stdout=f,
                    stderr=subprocess.STDOUT,
                    cwd=os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                    text=True
                )
                
                # 等待完成或超时
                try:
                    process.wait(timeout=config.timeout)
                    success = process.returncode == 0
                except subprocess.TimeoutExpired:
                    process.kill()
                    success = False
                    self.logger.warning(f"实验超时: {config.name}")
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            # 解析实验结果
            metrics = self._parse_experiment_log(log_file)
            
            result = ExperimentResult(
                config=config,
                success=success,
                start_time=start_time_str,
                end_time=end_time.isoformat(),
                duration=duration,
                final_accuracy=metrics.get('final_accuracy', 0.0),
                convergence_rounds=metrics.get('convergence_rounds', config.communication_rounds),
                total_time=duration,
                communication_cost=metrics.get('communication_cost', 0.0),
                storage_cost=metrics.get('storage_cost', 0.0),
                log_file=log_file,
                metrics=metrics
            )
            
            # 保存结果
            result_file = os.path.join(exp_dir, "result.json")
            with open(result_file, 'w') as f:
                json.dump(asdict(result), f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"实验完成: {config.name}, 成功: {success}, 耗时: {duration:.1f}s")
            return result
            
        except Exception as e:
            self.logger.error(f"实验失败: {config.name}, 错误: {e}")
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            return ExperimentResult(
                config=config,
                success=False,
                start_time=start_time_str,
                end_time=end_time.isoformat(),
                duration=duration,
                error_message=str(e),
                log_file=log_file
            )
    
    def _parse_experiment_log(self, log_file: str) -> Dict[str, Any]:
        """解析实验日志，提取关键指标"""
        metrics = {
            'final_accuracy': 0.0,
            'convergence_rounds': 0,
            'communication_cost': 0.0,
            'storage_cost': 0.0
        }
        
        try:
            with open(log_file, 'r') as f:
                content = f.read()
                
                # 简单的日志解析逻辑
                # 这里可以根据实际日志格式进行调整
                lines = content.split('\n')
                for line in lines:
                    if 'final accuracy' in line.lower() or 'test accuracy' in line.lower():
                        # 尝试提取准确率
                        try:
                            import re
                            match = re.search(r'(\d+\.?\d*)%?', line)
                            if match:
                                acc = float(match.group(1))
                                if acc > 1:  # 如果是百分比形式
                                    acc = acc / 100
                                metrics['final_accuracy'] = max(metrics['final_accuracy'], acc)
                        except:
                            pass
                    
                    if 'round' in line.lower() and 'communication' in line.lower():
                        # 尝试提取轮数信息
                        try:
                            import re
                            match = re.search(r'round\s*(\d+)', line.lower())
                            if match:
                                metrics['convergence_rounds'] = max(
                                    metrics['convergence_rounds'], 
                                    int(match.group(1))
                                )
                        except:
                            pass
                            
        except Exception as e:
            self.logger.warning(f"解析日志失败: {e}")
        
        return metrics
    
    def run_experiment_batch(self, configs: List[ExperimentConfig]) -> List[ExperimentResult]:
        """批量运行实验"""
        results = []
        
        self.logger.info(f"开始批量实验，共 {len(configs)} 个实验")
        
        for i, config in enumerate(configs, 1):
            self.logger.info(f"进度: {i}/{len(configs)}")
            result = self.run_single_experiment(config)
            if result:
                results.append(result)
        
        # 保存批量结果
        batch_result_file = os.path.join(self.output_dir, "batch_results.json")
        with open(batch_result_file, 'w') as f:
            json.dump([asdict(r) for r in results], f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"批量实验完成，成功: {sum(1 for r in results if r.success)}/{len(results)}")
        return results
