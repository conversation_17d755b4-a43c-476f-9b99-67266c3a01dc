#!/usr/bin/env python3
"""
验证数据集完整性
在服务器上运行，验证传输的数据集是否完整可用
"""

import os
import sys
from pathlib import Path
import torch
import torchvision
from torchvision import datasets, transforms

def check_pytorch_version():
    """检查PyTorch版本"""
    print("🔍 检查环境...")
    print(f"   PyTorch版本: {torch.__version__}")
    print(f"   Torchvision版本: {torchvision.__version__}")
    
    # 检查CUDA可用性
    if torch.cuda.is_available():
        print(f"   CUDA可用: {torch.cuda.device_count()} 个GPU")
    else:
        print("   CUDA不可用，将使用CPU")

def verify_dataset(dataset_class, name, data_dir, **kwargs):
    """验证单个数据集"""
    try:
        print(f"   🔍 验证 {name}...")
        
        # 尝试加载数据集（不下载）
        train_dataset = dataset_class(
            root=str(data_dir),
            train=True,
            download=False,  # 不下载，只验证
            transform=transforms.ToTensor(),
            **kwargs
        )
        
        test_dataset = dataset_class(
            root=str(data_dir),
            train=False,
            download=False,
            transform=transforms.ToTensor(),
            **kwargs
        )
        
        # 尝试加载一个样本
        train_sample = train_dataset[0]
        test_sample = test_dataset[0]
        
        print(f"      ✅ 训练集: {len(train_dataset)} 样本")
        print(f"      ✅ 测试集: {len(test_dataset)} 样本")
        print(f"      ✅ 样本形状: {train_sample[0].shape}")
        
        return True, len(train_dataset), len(test_dataset)
        
    except Exception as e:
        print(f"      ❌ 验证失败: {e}")
        return False, 0, 0

def check_directory_structure(data_dir):
    """检查目录结构"""
    print("📁 检查目录结构...")
    
    if not data_dir.exists():
        print(f"   ❌ 数据目录不存在: {data_dir}")
        return False
    
    print(f"   ✅ 数据目录存在: {data_dir}")
    
    # 检查预期的数据集目录
    expected_datasets = ['FashionMNIST', 'CIFAR10', 'CIFAR100', 'EMNIST']
    found_datasets = []
    
    for dataset_name in expected_datasets:
        dataset_path = data_dir / dataset_name
        if dataset_path.exists():
            found_datasets.append(dataset_name)
            print(f"   ✅ {dataset_name} 目录存在")
        else:
            print(f"   ⚠️ {dataset_name} 目录不存在")
    
    print(f"   发现 {len(found_datasets)}/{len(expected_datasets)} 个数据集目录")
    return len(found_datasets) > 0

def calculate_directory_size(data_dir):
    """计算目录大小"""
    total_size = 0
    file_count = 0
    
    for root, dirs, files in os.walk(data_dir):
        for file in files:
            file_path = os.path.join(root, file)
            try:
                size = os.path.getsize(file_path)
                total_size += size
                file_count += 1
            except:
                pass
    
    return total_size, file_count

def show_detailed_structure(data_dir):
    """显示详细的目录结构"""
    print("\n🌳 详细目录结构:")
    
    for item in sorted(data_dir.iterdir()):
        if item.is_dir():
            print(f"📁 {item.name}/")
            
            # 计算子目录大小
            subdir_size, subdir_files = calculate_directory_size(item)
            print(f"   大小: {subdir_size / (1024*1024):.1f} MB, 文件数: {subdir_files}")
            
            # 显示子目录结构
            try:
                for subitem in sorted(item.iterdir()):
                    if subitem.is_dir():
                        sub_size, sub_files = calculate_directory_size(subitem)
                        print(f"   📁 {subitem.name}/ ({sub_size / (1024*1024):.1f} MB, {sub_files} 文件)")
                    else:
                        size = subitem.stat().st_size / (1024*1024)
                        print(f"   📄 {subitem.name} ({size:.1f} MB)")
            except Exception as e:
                print(f"   ⚠️ 无法读取子目录: {e}")

def verify_all_datasets():
    """验证所有数据集"""
    
    print("🔍 VeryFL-PoL 数据集验证器")
    print("=" * 50)
    
    check_pytorch_version()
    
    # 检查数据目录
    data_dir = Path("./experiments/data")
    
    if not check_directory_structure(data_dir):
        print("\n❌ 数据目录结构检查失败")
        return False
    
    # 计算总大小
    total_size, total_files = calculate_directory_size(data_dir)
    print(f"\n📊 数据目录统计:")
    print(f"   总大小: {total_size / (1024*1024):.1f} MB")
    print(f"   总文件数: {total_files}")
    
    # 显示详细结构
    show_detailed_structure(data_dir)
    
    print(f"\n🔍 开始验证数据集...")
    print("=" * 30)
    
    # 验证每个数据集
    verification_results = []
    
    # 1. FashionMNIST
    success, train_count, test_count = verify_dataset(
        datasets.FashionMNIST, "FashionMNIST", data_dir
    )
    verification_results.append({
        'name': 'FashionMNIST',
        'success': success,
        'train_count': train_count,
        'test_count': test_count
    })
    
    # 2. CIFAR10
    success, train_count, test_count = verify_dataset(
        datasets.CIFAR10, "CIFAR10", data_dir
    )
    verification_results.append({
        'name': 'CIFAR10',
        'success': success,
        'train_count': train_count,
        'test_count': test_count
    })
    
    # 3. CIFAR100
    success, train_count, test_count = verify_dataset(
        datasets.CIFAR100, "CIFAR100", data_dir
    )
    verification_results.append({
        'name': 'CIFAR100',
        'success': success,
        'train_count': train_count,
        'test_count': test_count
    })
    
    # 4. EMNIST
    success, train_count, test_count = verify_dataset(
        datasets.EMNIST, "EMNIST", data_dir, split='letters'
    )
    verification_results.append({
        'name': 'EMNIST',
        'success': success,
        'train_count': train_count,
        'test_count': test_count
    })
    
    # 显示验证结果
    print("\n" + "=" * 50)
    print("📊 验证结果汇总:")
    
    successful_count = 0
    for result in verification_results:
        if result['success']:
            print(f"   ✅ {result['name']}: {result['train_count']} 训练样本, {result['test_count']} 测试样本")
            successful_count += 1
        else:
            print(f"   ❌ {result['name']}: 验证失败")
    
    print(f"\n成功验证: {successful_count}/{len(verification_results)} 个数据集")
    
    if successful_count == len(verification_results):
        print("\n🎉 所有数据集验证通过！")
        print("✅ 数据集已准备就绪，可以开始实验")
        
        # 提供下一步建议
        print(f"\n🚀 建议的下一步:")
        print(f"   1. 运行快速测试: python experiments/quick_test.py")
        print(f"   2. 运行基础实验: python experiments/master_experiment_runner.py --experiment basic")
        print(f"   3. 查看实验指南: cat experiments/EXPERIMENT_USAGE_GUIDE.md")
        
        return True
    else:
        print(f"\n⚠️ 部分数据集验证失败")
        print(f"💡 建议:")
        print(f"   1. 检查数据传输是否完整")
        print(f"   2. 重新下载失败的数据集")
        print(f"   3. 检查文件权限")
        
        return False

def quick_performance_test():
    """快速性能测试"""
    print(f"\n⚡ 快速性能测试...")
    
    try:
        data_dir = Path("./experiments/data")
        
        # 测试数据加载速度
        import time
        
        print("   测试 FashionMNIST 加载速度...")
        start_time = time.time()
        
        dataset = datasets.FashionMNIST(
            root=str(data_dir),
            train=True,
            download=False,
            transform=transforms.ToTensor()
        )
        
        # 加载前100个样本
        for i in range(min(100, len(dataset))):
            sample = dataset[i]
        
        load_time = time.time() - start_time
        print(f"   ✅ 加载100个样本耗时: {load_time:.2f}秒")
        
        if load_time < 5:
            print("   🚀 数据加载速度良好")
        else:
            print("   ⚠️ 数据加载较慢，可能影响实验性能")
            
    except Exception as e:
        print(f"   ❌ 性能测试失败: {e}")

def main():
    """主函数"""
    try:
        success = verify_all_datasets()
        
        if success:
            quick_performance_test()
            return 0
        else:
            return 1
            
    except KeyboardInterrupt:
        print("\n\n⏹️ 验证被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 验证过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
