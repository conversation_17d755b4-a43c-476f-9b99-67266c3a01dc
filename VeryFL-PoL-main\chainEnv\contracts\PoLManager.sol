// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

/**
 * @title PoLManager
 * @dev 管理学习证明(Proof-of-Learning)的智能合约
 * 负责存储PoL哈希、验证状态和激励分配
 */
contract PoLManager {
    
    // PoL记录结构
    struct PoLRecord {
        bytes32 polHash;        // PoL证明哈希
        string ipfsHash;        // IPFS存储哈希
        uint256 timestamp;      // 提交时间戳
        uint256 totalSteps;     // 训练总步数
        bool isVerified;        // 是否已验证
        bool isValid;           // 验证结果
        address verifier;       // 验证者地址
    }
    
    // 客户端信誉结构
    struct ClientReputation {
        uint256 totalSubmissions;   // 总提交次数
        uint256 validSubmissions;   // 有效提交次数
        uint256 reputationScore;    // 信誉分数 (0-1000)
        bool isBlacklisted;         // 是否被拉黑
    }
    
    // 激励池结构
    struct IncentivePool {
        uint256 totalRewards;      // 总奖励池
        uint256 distributedRewards; // 已分配奖励
        uint256 currentRound;      // 当前轮次
        mapping(address => uint256) roundRewards; // 每轮奖励
    }
    
    // 状态变量
    address public owner;
    address public federatedLearningContract; // FL主合约地址
    
    // 存储映射
    mapping(address => PoLRecord[]) public clientPoLRecords;
    mapping(address => ClientReputation) public clientReputations;
    mapping(bytes32 => bool) public usedPoLHashes; // 防止重复提交
    IncentivePool public incentivePool;
    
    // 配置参数
    uint256 public constant MIN_REPUTATION_SCORE = 100;
    uint256 public constant MAX_REPUTATION_SCORE = 1000;
    uint256 public constant REPUTATION_DECAY_RATE = 10; // 每轮衰减
    uint256 public verificationTimeLimit = 1 hours; // 验证时间限制
    
    // 事件定义
    event PoLSubmitted(
        address indexed client,
        bytes32 indexed polHash,
        string ipfsHash,
        uint256 totalSteps,
        uint256 timestamp
    );
    
    event PoLVerified(
        address indexed client,
        bytes32 indexed polHash,
        bool isValid,
        address indexed verifier,
        uint256 timestamp
    );
    
    event RewardDistributed(
        address indexed client,
        uint256 amount,
        uint256 round,
        uint256 timestamp
    );
    
    event ReputationUpdated(
        address indexed client,
        uint256 oldScore,
        uint256 newScore,
        uint256 timestamp
    );
    
    // 修饰符
    modifier onlyOwner() {
        require(msg.sender == owner, "Only owner can call this function");
        _;
    }
    
    modifier onlyFLContract() {
        require(msg.sender == federatedLearningContract, "Only FL contract can call this function");
        _;
    }
    
    modifier notBlacklisted(address client) {
        require(!clientReputations[client].isBlacklisted, "Client is blacklisted");
        _;
    }
    
    // 构造函数
    constructor() {
        owner = msg.sender;
        incentivePool.currentRound = 1;
    }
    
    /**
     * @dev 设置联邦学习主合约地址
     */
    function setFederatedLearningContract(address _flContract) external onlyOwner {
        federatedLearningContract = _flContract;
    }
    
    /**
     * @dev 客户端提交PoL证明哈希
     */
    function submitPoL(
        bytes32 _polHash,
        string memory _ipfsHash,
        uint256 _totalSteps
    ) external notBlacklisted(msg.sender) {
        require(_polHash != bytes32(0), "Invalid PoL hash");
        require(!usedPoLHashes[_polHash], "PoL hash already used");
        require(_totalSteps > 0, "Total steps must be positive");
        
        // 创建PoL记录
        PoLRecord memory newRecord = PoLRecord({
            polHash: _polHash,
            ipfsHash: _ipfsHash,
            timestamp: block.timestamp,
            totalSteps: _totalSteps,
            isVerified: false,
            isValid: false,
            verifier: address(0)
        });
        
        // 存储记录
        clientPoLRecords[msg.sender].push(newRecord);
        usedPoLHashes[_polHash] = true;
        
        // 更新提交统计
        clientReputations[msg.sender].totalSubmissions++;
        
        emit PoLSubmitted(msg.sender, _polHash, _ipfsHash, _totalSteps, block.timestamp);
    }
    
    /**
     * @dev 验证PoL证明
     */
    function verifyPoL(
        address _client,
        uint256 _recordIndex,
        bool _isValid
    ) external onlyFLContract {
        require(_recordIndex < clientPoLRecords[_client].length, "Invalid record index");
        
        PoLRecord storage record = clientPoLRecords[_client][_recordIndex];
        require(!record.isVerified, "PoL already verified");
        require(
            block.timestamp <= record.timestamp + verificationTimeLimit,
            "Verification time limit exceeded"
        );
        
        // 更新验证状态
        record.isVerified = true;
        record.isValid = _isValid;
        record.verifier = msg.sender;
        
        // 更新客户端信誉
        _updateReputation(_client, _isValid);
        
        emit PoLVerified(_client, record.polHash, _isValid, msg.sender, block.timestamp);
    }
    
    /**
     * @dev 更新客户端信誉分数
     */
    function _updateReputation(address _client, bool _isValid) internal {
        ClientReputation storage reputation = clientReputations[_client];
        uint256 oldScore = reputation.reputationScore;
        
        if (_isValid) {
            reputation.validSubmissions++;
            // 有效提交增加信誉
            if (reputation.reputationScore < MAX_REPUTATION_SCORE) {
                reputation.reputationScore += 50;
                if (reputation.reputationScore > MAX_REPUTATION_SCORE) {
                    reputation.reputationScore = MAX_REPUTATION_SCORE;
                }
            }
        } else {
            // 无效提交减少信誉
            if (reputation.reputationScore > 100) {
                reputation.reputationScore -= 100;
            } else {
                reputation.reputationScore = 0;
                reputation.isBlacklisted = true; // 信誉过低拉黑
            }
        }
        
        emit ReputationUpdated(_client, oldScore, reputation.reputationScore, block.timestamp);
    }
    
    /**
     * @dev 分配激励奖励
     */
    function distributeRewards(address[] memory _clients, uint256[] memory _rewards) 
        external onlyFLContract {
        require(_clients.length == _rewards.length, "Arrays length mismatch");
        
        uint256 totalDistribution = 0;
        for (uint256 i = 0; i < _rewards.length; i++) {
            totalDistribution += _rewards[i];
        }
        
        require(
            incentivePool.distributedRewards + totalDistribution <= incentivePool.totalRewards,
            "Insufficient reward pool"
        );
        
        // 分配奖励
        for (uint256 i = 0; i < _clients.length; i++) {
            if (_rewards[i] > 0) {
                incentivePool.roundRewards[_clients[i]] += _rewards[i];
                incentivePool.distributedRewards += _rewards[i];
                
                emit RewardDistributed(
                    _clients[i], 
                    _rewards[i], 
                    incentivePool.currentRound, 
                    block.timestamp
                );
            }
        }
    }
    
    /**
     * @dev 添加奖励到激励池
     */
    function addToIncentivePool() external payable onlyOwner {
        incentivePool.totalRewards += msg.value;
    }
    
    /**
     * @dev 开始新一轮
     */
    function startNewRound() external onlyFLContract {
        incentivePool.currentRound++;
        
        // 衰减所有客户端信誉分数
        // 注意：这里简化处理，实际应该遍历所有客户端
        // 在实际实现中可能需要分批处理以避免gas限制
    }
    
    /**
     * @dev 获取客户端PoL记录数量
     */
    function getClientPoLCount(address _client) external view returns (uint256) {
        return clientPoLRecords[_client].length;
    }
    
    /**
     * @dev 获取客户端信誉信息
     */
    function getClientReputation(address _client) external view returns (
        uint256 totalSubmissions,
        uint256 validSubmissions,
        uint256 reputationScore,
        bool isBlacklisted
    ) {
        ClientReputation memory reputation = clientReputations[_client];
        return (
            reputation.totalSubmissions,
            reputation.validSubmissions,
            reputation.reputationScore,
            reputation.isBlacklisted
        );
    }
    
    /**
     * @dev 获取激励池信息
     */
    function getIncentivePoolInfo() external view returns (
        uint256 totalRewards,
        uint256 distributedRewards,
        uint256 currentRound
    ) {
        return (
            incentivePool.totalRewards,
            incentivePool.distributedRewards,
            incentivePool.currentRound
        );
    }
    
    /**
     * @dev 紧急情况下重置客户端黑名单状态
     */
    function resetBlacklist(address _client) external onlyOwner {
        clientReputations[_client].isBlacklisted = false;
        clientReputations[_client].reputationScore = MIN_REPUTATION_SCORE;
    }
}
