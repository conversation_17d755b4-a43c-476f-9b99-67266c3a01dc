#!/usr/bin/env python3
"""
重启实验脚本 - 基于失败分析的智能重启
"""

import os
import sys
import json
import time
import shutil
from pathlib import Path

sys.path.append('.')

from experiments.parallel_executor import ParallelExecutor, ExperimentConfig

def analyze_failed_experiments():
    """分析失败的实验"""
    print("🔍 分析失败的实验...")
    
    report_path = "experiments/experiments/master_experiment_results_20250729_222536/parallel_results/experiment_report.json"
    if not os.path.exists(report_path):
        print("❌ 找不到实验报告")
        return []
    
    with open(report_path, 'r', encoding='utf-8') as f:
        report = json.load(f)
    
    failed_experiments = []
    for exp in report['experiments']:
        if not exp['success']:
            config = exp['config']
            
            # 分析失败原因
            error_msg = exp.get('error_message', '')
            
            if "timeout" in error_msg.lower():
                # 超时的实验，增加超时时间
                config['timeout'] = 7200  # 2小时
                config['communication_rounds'] = min(config.get('communication_rounds', 5), 3)  # 减少轮次
            elif "transaction underpriced" in error_msg.lower():
                # Gas问题，已经在修复脚本中解决
                config['timeout'] = 3600  # 1小时
            else:
                # 其他错误，保守设置
                config['timeout'] = 3600
                config['communication_rounds'] = min(config.get('communication_rounds', 5), 2)
            
            # 减少客户端数量以提高稳定性
            config['client_num'] = min(config.get('client_num', 10), 5)
            
            failed_experiments.append(ExperimentConfig(**config))
    
    print(f"   发现 {len(failed_experiments)} 个失败的实验")
    return failed_experiments

def create_recovery_experiments():
    """创建恢复实验配置"""
    print("🔧 创建恢复实验配置...")
    
    # 基于失败分析的保守配置
    recovery_experiments = [
        # 基础功能验证
        ExperimentConfig(
            name="recovery_basic_test",
            benchmark="FashionMNIST",
            communication_rounds=1,
            enable_pol=False,
            client_num=3,
            timeout=1800,
        ),
        
        # PoL功能验证
        ExperimentConfig(
            name="recovery_pol_test",
            benchmark="PoLFashionMNIST", 
            communication_rounds=1,
            enable_pol=True,
            client_num=3,
            timeout=3600,
        ),
        
        # 性能对比（简化版）
        ExperimentConfig(
            name="recovery_baseline_fashion",
            benchmark="FashionMNIST",
            communication_rounds=2,
            enable_pol=False,
            client_num=5,
            timeout=3600,
        ),
        
        ExperimentConfig(
            name="recovery_pol_fashion",
            benchmark="PoLFashionMNIST",
            communication_rounds=2,
            enable_pol=True,
            client_num=5,
            timeout=7200,
        ),
    ]
    
    print(f"   创建了 {len(recovery_experiments)} 个恢复实验")
    return recovery_experiments

def check_prerequisites():
    """检查前置条件"""
    print("🔍 检查前置条件...")
    
    # 检查区块链连接
    try:
        import requests
        response = requests.get("http://localhost:8545", timeout=5)
        print("   ✅ 区块链连接正常")
    except Exception as e:
        print(f"   ❌ 区块链连接失败: {e}")
        print("   💡 请先启动Ganache: ganache-cli --host 0.0.0.0 --port 8545 --accounts 20 --gasLimit ********")
        return False
    
    # 检查数据集
    data_dir = Path("experiments/data")
    if data_dir.exists():
        print("   ✅ 数据目录存在")
    else:
        print("   ⚠️ 数据目录不存在，将在实验中下载")
    
    # 检查修复是否已应用
    connection_manager_path = "chainfl/connection_manager.py"
    if os.path.exists(connection_manager_path):
        with open(connection_manager_path, 'r') as f:
            content = f.read()
        if "************" in content:  # 100 gwei
            print("   ✅ Gas价格修复已应用")
        else:
            print("   ⚠️ Gas价格修复未应用，建议先运行 fix_experiment_issues.py")
    
    return True

def backup_old_results():
    """备份旧的实验结果"""
    print("🗂️ 备份旧的实验结果...")
    
    old_results_dir = "experiments/experiments/master_experiment_results_20250729_222536"
    if os.path.exists(old_results_dir):
        backup_dir = f"{old_results_dir}_backup_{int(time.time())}"
        shutil.move(old_results_dir, backup_dir)
        print(f"   ✅ 旧结果已备份到: {backup_dir}")
    else:
        print("   ℹ️ 没有找到旧的实验结果")

def run_recovery_experiments():
    """运行恢复实验"""
    print("🚀 开始运行恢复实验...")
    
    if not check_prerequisites():
        return False
    
    # 备份旧结果
    backup_old_results()
    
    # 获取恢复实验配置
    experiments = create_recovery_experiments()
    
    # 创建执行器（降低并行度以提高稳定性）
    executor = ParallelExecutor(max_parallel=1)  # 串行执行
    
    print(f"\n🎯 将运行 {len(experiments)} 个恢复实验")
    print("   执行模式: 串行（避免资源冲突）")
    
    try:
        results = executor.run_experiments(experiments)
        
        # 分析结果
        successful = sum(1 for r in results if r.get('success', False))
        total = len(results)
        
        print(f"\n📊 恢复实验结果:")
        print(f"   成功: {successful}/{total} ({successful/total*100:.1f}%)")
        
        if successful == total:
            print("🎉 所有恢复实验成功！系统已修复")
            return True
        elif successful > 0:
            print("⚠️ 部分实验成功，系统部分修复")
            return True
        else:
            print("❌ 所有实验失败，需要进一步诊断")
            return False
            
    except Exception as e:
        print(f"❌ 恢复实验异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_failed_experiments():
    """重新运行之前失败的实验"""
    print("🔄 重新运行之前失败的实验...")
    
    failed_experiments = analyze_failed_experiments()
    if not failed_experiments:
        print("   ℹ️ 没有找到失败的实验")
        return True
    
    # 限制重试实验数量
    retry_experiments = failed_experiments[:3]  # 只重试前3个
    
    print(f"🎯 将重试 {len(retry_experiments)} 个实验")
    
    executor = ParallelExecutor(max_parallel=1)  # 串行执行
    
    try:
        results = executor.run_experiments(retry_experiments)
        
        successful = sum(1 for r in results if r.get('success', False))
        total = len(results)
        
        print(f"\n📊 重试结果:")
        print(f"   成功: {successful}/{total} ({successful/total*100:.1f}%)")
        
        return successful > 0
        
    except Exception as e:
        print(f"❌ 重试实验异常: {e}")
        return False

def main():
    """主函数"""
    print("🔄 实验恢复系统")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        mode = sys.argv[1]
    else:
        print("请选择运行模式:")
        print("  1. recovery - 运行恢复实验（推荐）")
        print("  2. retry - 重试失败的实验")
        print("  3. both - 先恢复再重试")
        
        choice = input("输入选择 (1/2/3): ").strip()
        mode_map = {'1': 'recovery', '2': 'retry', '3': 'both'}
        mode = mode_map.get(choice, 'recovery')
    
    success = False
    
    if mode in ['recovery', 'both']:
        success = run_recovery_experiments()
    
    if mode in ['retry', 'both']:
        if mode == 'both' and not success:
            print("\n⚠️ 恢复实验失败，跳过重试")
        else:
            retry_success = run_failed_experiments()
            success = success or retry_success
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 实验恢复完成！")
        print("\n🚀 建议下一步:")
        print("   python experiments/master_experiment_runner.py --experiment basic")
    else:
        print("❌ 实验恢复失败")
        print("\n🔧 建议:")
        print("   1. 检查Ganache是否正常运行")
        print("   2. 运行 python experiments/fix_experiment_issues.py")
        print("   3. 检查网络连接")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
