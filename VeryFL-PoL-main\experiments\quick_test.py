#!/usr/bin/env python3
"""
快速测试脚本 - 验证修复效果
"""

import sys
import os
sys.path.append('.')

from experiments.parallel_executor import ParallelExecutor, ExperimentConfig

def test_blockchain_connection():
    """测试区块链连接"""
    print("🔍 测试区块链连接...")
    try:
        import requests
        response = requests.get("http://localhost:8545", timeout=5)
        print(f"   ✅ 区块链连接正常: {response.status_code}")
        return True
    except Exception as e:
        print(f"   ❌ 区块链连接失败: {e}")
        return False

def test_data_availability():
    """测试数据集可用性"""
    print("🔍 测试数据集可用性...")
    try:
        import torch
        import torchvision
        from torchvision import datasets, transforms
        
        transform = transforms.Compose([transforms.ToTensor()])
        
        # 测试FashionMNIST
        dataset = datasets.FashionMNIST(
            root="./experiments/data",
            train=True,
            download=False,  # 不下载，只检查
            transform=transform
        )
        print(f"   ✅ FashionMNIST可用: {len(dataset)} 样本")
        return True
    except Exception as e:
        print(f"   ⚠️ 数据集问题: {e}")
        return False

def run_quick_test():
    """运行快速测试"""
    print("🚀 开始快速测试...")
    
    # 检查前置条件
    if not test_blockchain_connection():
        print("❌ 请先启动Ganache区块链")
        return False
    
    test_data_availability()
    
    # 运行简单的PoL测试
    print("🔍 运行简单的PoL测试...")
    
    config = ExperimentConfig(
        name="quick_pol_test",
        benchmark="PoLFashionMNIST",
        communication_rounds=1,
        enable_pol=True,
        client_num=2,
        timeout=1800,
    )
    
    executor = ParallelExecutor(max_parallel=1)
    
    try:
        results = executor.run_experiments([config])
        
        if results and len(results) > 0 and results[0].get('success', False):
            print("✅ 快速测试通过！")
            return True
        else:
            print("❌ 快速测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

if __name__ == "__main__":
    success = run_quick_test()
    sys.exit(0 if success else 1)
