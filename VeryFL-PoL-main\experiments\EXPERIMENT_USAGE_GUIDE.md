# VeryFL-PoL 实验系统使用指南

## 🎯 文件路径统一说明

**重要**: 所有实验生成的文件现在都统一保存在 `experiments/` 目录下，不会再散落到项目各处！

### 📁 目录结构
```
experiments/
├── data/                    # 数据集文件
├── log/                     # 日志文件
├── pol_data/               # PoL证明数据
├── results/                # 基础实验结果
├── models/                 # 保存的模型
├── monitor/                # 监控数据
├── sota_results/           # SOTA对比结果
├── compression_results/    # 压缩对比结果
├── network_results/        # 网络模拟结果
├── ablation_results/       # 消融研究结果
├── parallel_results_*/     # 并行实验结果
├── master_experiment_*/    # 主实验控制器结果
└── temp/                   # 临时文件
```

## 🚀 快速开始

### 1. 环境准备
```bash
cd VeryFL-PoL-main

# 运行路径修复脚本（首次使用必须）
python fix_file_paths.py

# 启动区块链网络
./start_ganache.sh
```

### 2. 运行实验

#### 单个实验
```bash
# PoL实验
python test.py --benchmark PoLFashionMNIST --communication-rounds 2 --enable-pol

# 基线对比
python test.py --benchmark FashionMNIST --communication-rounds 2

# 攻击实验
python test.py --benchmark PoLFashionMNIST --communication-rounds 3 --enable-pol \
    --attack-type free_rider --malicious-ratio 0.2
```

#### 并行实验套件
```bash
cd experiments

# 快速测试（推荐新手）
python parallel_executor.py --suite quick --max-parallel 2

# 性能对比实验
python parallel_executor.py --suite performance --max-parallel 2

# 攻击防御实验
python parallel_executor.py --suite attack_defense --max-parallel 1

# 压缩效果实验
python parallel_executor.py --suite compression --max-parallel 2
```

## 📊 实验套件详解

### Quick套件 (测试用)
- **用途**: 快速验证环境配置
- **时间**: 约5-10分钟
- **输出**: `experiments/parallel_results_*/`

### Performance套件 (性能对比)
- **包含**: 6个实验，对比PoL与基线在3个数据集上的性能
- **时间**: 约30-60分钟
- **输出**: `experiments/parallel_results_*/`

### Attack_Defense套件 (攻击防御)
- **包含**: 7个攻击场景，验证PoL防御效果
- **时间**: 约60-120分钟
- **输出**: `experiments/parallel_results_*/`

### Compression套件 (压缩分析)
- **包含**: 3种压缩方案对比
- **时间**: 约45-90分钟
- **输出**: `experiments/parallel_results_*/`

## 🔧 高级实验

### SOTA方法对比
```bash
cd experiments
python sota_comparison.py --datasets FashionMNIST CIFAR10 \
    --attack-types free_rider partial_free_rider \
    --malicious-ratios 0.1 0.2 0.3
```
**输出**: `experiments/sota_results/`

### 压缩效果分析
```bash
cd experiments
python compression_comparison.py --test-scenarios small_model medium_model
```
**输出**: `experiments/compression_results/`

### 网络可扩展性测试
```bash
cd experiments
python network_simulator.py --client-counts 10 20 50 \
    --network-conditions ideal high_latency low_bandwidth
```
**输出**: `experiments/network_results/`

### 消融研究
```bash
cd experiments
python ablation_study.py --datasets FashionMNIST CIFAR10
```
**输出**: `experiments/ablation_results/`

### 主实验控制器
```bash
cd experiments
# 运行所有5个实验套件
python master_experiment_runner.py --experiment all

# 运行特定实验
python master_experiment_runner.py --experiment 1  # 性能对比
python master_experiment_runner.py --experiment 2  # 攻击防御
```
**输出**: `experiments/master_experiment_results_*/`

## 📈 结果查看

### 实验报告
```bash
# 查看JSON格式报告
cat experiments/parallel_results_*/experiment_report.json | jq .

# 查看特定实验日志
tail -f experiments/parallel_results_*/pol_fashion_mnist_1/experiment.log
```

### 统计分析
```bash
cd experiments
python statistical_analyzer.py --input-dir ./sota_results
```

## 🧹 文件管理

### 清理生成文件
```bash
cd experiments
python clean_generated_files.py
```

### 检查文件分布
```bash
# 查看experiments目录结构
tree experiments/ -I "__pycache__|*.pyc"

# 检查文件数量
find experiments/ -type f | wc -l
```

## ⚡ 性能优化

### 并行度设置
- **CPU环境**: `--max-parallel 2-4`
- **GPU环境**: `--max-parallel 1-2` (避免显存竞争)
- **内存限制**: 每个实验约需2-4GB内存

### 实验顺序建议
1. 先运行 `fix_file_paths.py` 修复路径
2. 运行Quick套件验证环境
3. 运行Performance套件了解基础性能
4. 根据需要运行专项实验

## 🚨 常见问题

### 文件路径问题
如果发现文件还是散落在各处：
```bash
# 重新运行路径修复脚本
python fix_file_paths.py

# 手动移动散落文件
mv data experiments/ 2>/dev/null || true
mv log experiments/ 2>/dev/null || true
mv results experiments/ 2>/dev/null || true
```

### 区块链连接问题
```bash
# 检查Ganache是否运行
ps aux | grep ganache

# 重启区块链网络
./start_ganache.sh
```

### 内存不足
```bash
# 减少并行度
python parallel_executor.py --suite quick --max-parallel 1
```

## 📞 监控和调试

实验运行时会自动生成：
- **系统资源监控** (CPU, 内存, GPU使用率)
- **实验进度跟踪** (当前轮次, 剩余时间)
- **性能指标记录** (准确率, 训练时间, 通信开销)

所有监控数据都保存在对应的输出目录中。

---

**重要提醒**: 现在所有生成的文件都会统一保存在 `experiments/` 目录下，不会再让项目目录变乱！
