#!/usr/bin/env python3
"""
攻击模拟器
用于模拟各种搭便车攻击和恶意行为
"""

import torch
import torch.nn as nn
import numpy as np
import random
from typing import Dict, Any, Tuple
import logging

logger = logging.getLogger(__name__)

class AttackSimulator:
    """攻击模拟器主类"""
    
    def __init__(self, attack_type: str, malicious_ratio: float):
        self.attack_type = attack_type
        self.malicious_ratio = malicious_ratio
        self.malicious_clients = set()
        
    def setup_malicious_clients(self, total_clients: int):
        """设置恶意客户端"""
        num_malicious = int(total_clients * self.malicious_ratio)
        self.malicious_clients = set(random.sample(range(total_clients), num_malicious))
        logger.info(f"设置 {num_malicious}/{total_clients} 个恶意客户端: {self.malicious_clients}")
    
    def is_malicious_client(self, client_id: int) -> bool:
        """检查是否为恶意客户端"""
        return client_id in self.malicious_clients
    
    def apply_attack(self, client_id: int, model: nn.Module, 
                    dataloader, optimizer, criterion) -> Tuple[nn.Module, Dict[str, Any]]:
        """应用攻击"""
        if not self.is_malicious_client(client_id):
            # 正常客户端，正常训练
            return self._normal_training(model, dataloader, optimizer, criterion)
        
        # 恶意客户端，应用攻击
        if self.attack_type == "free_rider":
            return self._free_rider_attack(model, dataloader, optimizer, criterion)
        elif self.attack_type == "partial_free_rider":
            return self._partial_free_rider_attack(model, dataloader, optimizer, criterion)
        elif self.attack_type == "data_poison":
            return self._data_poison_attack(model, dataloader, optimizer, criterion)
        elif self.attack_type == "model_poison":
            return self._model_poison_attack(model, dataloader, optimizer, criterion)
        else:
            # 默认正常训练
            return self._normal_training(model, dataloader, optimizer, criterion)
    
    def _normal_training(self, model: nn.Module, dataloader, 
                        optimizer, criterion) -> Tuple[nn.Module, Dict[str, Any]]:
        """正常训练"""
        model.train()
        total_loss = 0.0
        num_batches = 0
        
        for batch_data, batch_targets in dataloader:
            optimizer.zero_grad()
            outputs = model(batch_data)
            loss = criterion(outputs, batch_targets)
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
        
        avg_loss = total_loss / max(num_batches, 1)
        
        return model, {
            'training_loss': avg_loss,
            'num_batches': num_batches,
            'attack_applied': False
        }
    
    def _free_rider_attack(self, model: nn.Module, dataloader, 
                          optimizer, criterion) -> Tuple[nn.Module, Dict[str, Any]]:
        """完全搭便车攻击：不进行任何训练"""
        logger.debug("应用完全搭便车攻击")
        
        # 不进行任何训练，直接返回原模型
        return model, {
            'training_loss': 0.0,
            'num_batches': 0,
            'attack_applied': True,
            'attack_type': 'free_rider'
        }
    
    def _partial_free_rider_attack(self, model: nn.Module, dataloader, 
                                  optimizer, criterion) -> Tuple[nn.Module, Dict[str, Any]]:
        """部分搭便车攻击：只训练很少的步数"""
        logger.debug("应用部分搭便车攻击")
        
        model.train()
        total_loss = 0.0
        num_batches = 0
        
        # 只训练前10%的数据
        max_batches = max(1, len(dataloader) // 10)
        
        for i, (batch_data, batch_targets) in enumerate(dataloader):
            if i >= max_batches:
                break
                
            optimizer.zero_grad()
            outputs = model(batch_data)
            loss = criterion(outputs, batch_targets)
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
        
        avg_loss = total_loss / max(num_batches, 1)
        
        return model, {
            'training_loss': avg_loss,
            'num_batches': num_batches,
            'attack_applied': True,
            'attack_type': 'partial_free_rider'
        }
    
    def _data_poison_attack(self, model: nn.Module, dataloader, 
                           optimizer, criterion) -> Tuple[nn.Module, Dict[str, Any]]:
        """数据投毒攻击：使用错误的标签进行训练"""
        logger.debug("应用数据投毒攻击")
        
        model.train()
        total_loss = 0.0
        num_batches = 0
        
        for batch_data, batch_targets in dataloader:
            # 随机化标签（数据投毒）
            poisoned_targets = torch.randint_like(batch_targets, 0, 10)
            
            optimizer.zero_grad()
            outputs = model(batch_data)
            loss = criterion(outputs, poisoned_targets)
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
        
        avg_loss = total_loss / max(num_batches, 1)
        
        return model, {
            'training_loss': avg_loss,
            'num_batches': num_batches,
            'attack_applied': True,
            'attack_type': 'data_poison'
        }
    
    def _model_poison_attack(self, model: nn.Module, dataloader, 
                            optimizer, criterion) -> Tuple[nn.Module, Dict[str, Any]]:
        """模型投毒攻击：在正常训练后添加恶意扰动"""
        logger.debug("应用模型投毒攻击")
        
        # 先进行正常训练
        model, training_info = self._normal_training(model, dataloader, optimizer, criterion)
        
        # 然后添加恶意扰动
        with torch.no_grad():
            for param in model.parameters():
                # 添加高斯噪声
                noise = torch.randn_like(param) * 0.1
                param.add_(noise)
        
        training_info.update({
            'attack_applied': True,
            'attack_type': 'model_poison'
        })
        
        return model, training_info

class AttackDetector:
    """攻击检测器"""
    
    def __init__(self, detection_method: str = "pol"):
        self.detection_method = detection_method
        self.client_histories = {}
    
    def detect_attack(self, client_id: int, model_update: Dict[str, Any], 
                     pol_proof: Dict[str, Any] = None) -> Dict[str, Any]:
        """检测攻击"""
        if self.detection_method == "pol":
            return self._pol_based_detection(client_id, model_update, pol_proof)
        elif self.detection_method == "statistical":
            return self._statistical_detection(client_id, model_update)
        else:
            return {'is_malicious': False, 'confidence': 0.0}
    
    def _pol_based_detection(self, client_id: int, model_update: Dict[str, Any], 
                            pol_proof: Dict[str, Any]) -> Dict[str, Any]:
        """基于PoL的攻击检测"""
        if pol_proof is None:
            return {'is_malicious': True, 'confidence': 1.0, 'reason': 'No PoL proof'}
        
        # 检查PoL证明的完整性
        required_fields = ['total_steps', 'checkpoints', 'batch_indices']
        for field in required_fields:
            if field not in pol_proof:
                return {'is_malicious': True, 'confidence': 0.9, 'reason': f'Missing {field}'}
        
        # 检查训练步数是否合理
        total_steps = pol_proof.get('total_steps', 0)
        if total_steps < 10:  # 假设正常训练至少需要10步
            return {'is_malicious': True, 'confidence': 0.8, 'reason': 'Too few training steps'}
        
        # 检查检查点数量是否合理
        checkpoints = pol_proof.get('checkpoints', [])
        expected_checkpoints = max(1, total_steps // pol_proof.get('save_freq', 20))
        if len(checkpoints) < expected_checkpoints * 0.5:  # 允许50%的容忍度
            return {'is_malicious': True, 'confidence': 0.7, 'reason': 'Too few checkpoints'}
        
        return {'is_malicious': False, 'confidence': 0.1}
    
    def _statistical_detection(self, client_id: int, model_update: Dict[str, Any]) -> Dict[str, Any]:
        """基于统计的攻击检测"""
        # 基于统计的攻击检测：分析模型更新模式
        if 'model_state' not in model_update:
            return {'is_malicious': True, 'confidence': 0.8}
        
        # 计算模型参数的变化幅度
        total_change = 0.0
        param_count = 0
        
        for param in model_update['model_state'].values():
            if isinstance(param, torch.Tensor):
                total_change += torch.norm(param).item()
                param_count += param.numel()
        
        avg_change = total_change / max(param_count, 1)
        
        # 如果变化太小，可能是搭便车
        if avg_change < 1e-6:
            return {'is_malicious': True, 'confidence': 0.6, 'reason': 'No model update'}
        
        # 如果变化太大，可能是投毒
        if avg_change > 1.0:
            return {'is_malicious': True, 'confidence': 0.7, 'reason': 'Excessive model change'}
        
        return {'is_malicious': False, 'confidence': 0.2}
