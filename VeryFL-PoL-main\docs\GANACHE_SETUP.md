# Ganache配置指南

## 🚨 重要：账户数量问题

VeryFL-PoL实验需要20个客户端，但Ganache默认只提供10个账户。这会导致：
- 多个客户端共享同一个区块链地址
- PoL记录混淆
- 验证结果不准确

## ✅ 解决方案

### 方法1：使用提供的启动脚本（推荐）

```bash
# 在VeryFL-main目录下运行
./start_ganache.sh
```

这个脚本会：
- 自动配置25个账户
- 设置正确的Gas参数
- 检查端口冲突

### 方法2：手动启动Ganache

```bash
# 停止现有的Ganache进程
pkill -f ganache

# 启动Ganache，配置25个账户
ganache --accounts 25 --deterministic --host 0.0.0.0 --port 8545 --gasLimit ********
```

### 方法3：修改实验配置

如果无法增加账户数量，可以减少客户端数量：

```python
# 在experiments/experiment_framework.py中修改
client_num = 9  # 改为9个客户端（适配10个Ganache账户）
```

## 🔍 验证配置

启动Ganache后，可以验证账户数量：

```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"eth_accounts","params":[],"id":1}' \
  http://localhost:8545
```

应该看到25个账户地址。

## 📊 当前问题说明

### 检查点 vs PoL记录

- **检查点**: PoL证明内部的训练步骤记录（每20步一个）
- **PoL记录**: 区块链上的完整PoL证明（每轮一个）

正常情况：
```
验证了12个检查点 ← 一个PoL证明内的检查点
客户端有2条PoL记录 ← 区块链上的完整证明数量
```

### 聚合器验证 vs 客户端自验证

| 类型 | 验证比例 | 目的 |
|------|----------|------|
| 客户端自验证 | 10% | 快速检查格式正确性 |
| 聚合器验证 | 50% | 确保证明真实性 |

### PoL记录数量不一致

如果看到不同客户端有不同数量的PoL记录，可能是：
1. **账户共享**：多个客户端使用同一个区块链地址
2. **历史记录**：之前实验的残留数据
3. **重复提交**：客户端多次提交证明

## 🛠️ 故障排除

### 1. 端口被占用
```bash
# 查看占用端口8545的进程
lsof -i :8545

# 停止Ganache进程
pkill -f ganache
```

### 2. 账户不足警告
```
当前可用账户数: 9，需要创建更多账户
```
解决：使用上述方法1或2重新启动Ganache

### 3. 地址映射混乱
如果看到客户端20提交但验证客户端1，这是正常的：
- 客户端按顺序提交（1-20）
- 聚合器按顺序验证（1-20）
- 这是异步处理的正常行为

## 📝 最佳实践

1. **总是使用25个账户启动Ganache**
2. **每次实验前重启Ganache清理状态**
3. **监控日志确认地址映射正确**
4. **验证PoL记录数量合理**
