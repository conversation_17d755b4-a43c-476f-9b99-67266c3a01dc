# 🔧 VeryFL-PoL 环境配置指南

## 🚨 NumPy兼容性问题解决方案

### 问题描述
NumPy 2.x版本与pandas、scipy等库存在兼容性问题，导致：
- `AttributeError: module 'numpy' has no attribute '__version__'`
- `AttributeError: module 'numpy' has no attribute 'ndarray'`
- pandas导入失败

### 🔧 解决方案

#### 方案1: 自动修复脚本（推荐）
```bash
cd VeryFL-PoL/VeryFL-PoL-main
python fix_numpy_environment.py
```

#### 方案2: 手动修复
```bash
# 1. 卸载冲突版本
pip uninstall numpy pandas scipy -y

# 2. 安装兼容版本
pip install numpy==1.24.3
pip install scipy==1.10.1  
pip install pandas==2.0.3
pip install matplotlib==3.7.2
pip install seaborn==0.12.2
pip install scikit-learn==1.3.0

# 3. 验证安装
python -c "import numpy as np; print(f'NumPy版本: {np.__version__}')"
python -c "import pandas as pd; print(f'Pandas版本: {pd.__version__}')"
```

#### 方案3: 使用兼容性requirements文件
```bash
pip install -r requirements_compatible.txt
```

### 🧪 验证修复
```bash
# 测试基础导入
python -c "
import numpy as np
import pandas as pd
import scipy
print('✅ 所有库导入成功')
print(f'NumPy: {np.__version__}')
print(f'Pandas: {pd.__version__}')
print(f'SciPy: {scipy.__version__}')
"

# 测试VeryFL功能
cd experiments
python enhanced_parallel_runner.py --dry-run
```

## 📦 完整环境配置

### 系统要求
- Python 3.8+
- CUDA 11.0+ (可选，用于GPU加速)
- Node.js 16+ (用于Ganache)

### 依赖安装

#### 1. 核心Python依赖
```bash
# 科学计算库（兼容版本）
pip install numpy==1.24.3 scipy==1.10.1 pandas==2.0.3

# 机器学习库
pip install torch torchvision scikit-learn==1.3.0

# 可视化库
pip install matplotlib==3.7.2 seaborn==0.12.2

# 工具库
pip install tqdm psutil
```

#### 2. 区块链环境
```bash
# 安装Node.js和Ganache
npm install -g ganache

# 安装Brownie（可选，用于完整区块链功能）
pip install eth-brownie
```

#### 3. VeryFL-PoL特定依赖
```bash
# 已包含在上述安装中
```

### 🚀 快速启动

#### 1. 环境检查
```bash
cd VeryFL-PoL/VeryFL-PoL-main
python fix_numpy_environment.py
```

#### 2. 启动区块链（可选）
```bash
./start_ganache.sh
```

#### 3. 运行实验
```bash
cd experiments

# 快速测试
python enhanced_parallel_runner.py --suite quick --dry-run

# 实际运行
python enhanced_parallel_runner.py --suite quick
```

## 🔄 执行器选择建议

### 新执行器 vs 老执行器对比

| 特性 | enhanced_parallel_runner | parallel_runner | run_all_experiments |
|------|-------------------------|-----------------|-------------------|
| 并行执行 | ✅ | ✅ | ❌ |
| 模式选择 | ✅ | ❌ | ✅ |
| 自动分析 | ✅ | ❌ | ✅ |
| 错误处理 | ✅ | ⚠️ | ✅ |
| 用户体验 | ✅ | ⚠️ | ✅ |
| 稳定性 | 🧪 | ✅ | ✅ |

### 使用建议

#### 🎯 推荐使用新执行器的场景
- 日常开发和测试
- 需要快速验证功能
- 希望并行加速实验
- 需要自动结果分析

```bash
# 新执行器使用示例
python enhanced_parallel_runner.py --suite medium --max-parallel 4
```

#### 🛡️ 保留老执行器的场景
- 重要的生产实验
- 需要最高稳定性
- 作为备份方案

```bash
# 老执行器使用示例
python parallel_runner.py --max-parallel 4
python run_all_experiments.py --suite medium
```

### 🗂️ 代码清理建议

#### 阶段1: 并存测试（当前阶段）
```
experiments/
├── enhanced_parallel_runner.py    # 新版本（主要使用）
├── parallel_runner.py             # 老版本（备份）
├── run_all_experiments.py         # 老版本（备份）
└── ...
```

#### 阶段2: 充分验证后（1-2周后）
如果新执行器稳定运行，可以考虑：
```bash
# 重命名老版本为备份
mv parallel_runner.py parallel_runner_legacy.py
mv run_all_experiments.py run_all_experiments_legacy.py

# 或者移动到legacy目录
mkdir legacy
mv parallel_runner.py legacy/
mv run_all_experiments.py legacy/
```

#### 阶段3: 完全替换（1个月后）
如果新执行器完全稳定，可以删除老版本。

## 🐛 常见问题解决

### 1. NumPy版本问题
```bash
# 症状：AttributeError: module 'numpy' has no attribute '__version__'
# 解决：运行修复脚本
python fix_numpy_environment.py
```

### 2. 区块链账户不足
```bash
# 症状：账户不足！客户端数量超过可用账户数
# 解决：使用新的start_ganache.sh（100个账户）
./start_ganache.sh
```

### 3. 实验失败
```bash
# 症状：实验返回空结果或异常
# 解决：检查日志，使用新执行器的错误处理
python enhanced_parallel_runner.py --experiments basic --dry-run
```

### 4. 内存不足
```bash
# 症状：并行实验时内存溢出
# 解决：减少并行数
python enhanced_parallel_runner.py --max-parallel 2
```

## 📞 技术支持

如果遇到问题：
1. 首先运行 `python fix_numpy_environment.py`
2. 查看实验日志文件
3. 使用 `--dry-run` 模式检查配置
4. 尝试减少并行数或使用quick模式

## 🎯 总结

- **环境问题**：使用修复脚本解决NumPy兼容性
- **执行器选择**：新执行器用于日常，老执行器作备份
- **代码清理**：分阶段进行，确保稳定性
- **问题排查**：使用提供的工具和指南
