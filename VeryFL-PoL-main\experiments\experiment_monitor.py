#!/usr/bin/env python3
"""
VeryFL-PoL 实验监控系统
记录实验的详细状态，支持离线分析
"""

import os
import sys
import json
import time
import logging
import psutil
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

@dataclass
class SystemInfo:
    """系统信息"""
    cpu_count: int
    memory_total: float  # GB
    gpu_info: List[Dict[str, Any]]
    python_version: str
    timestamp: str

@dataclass
class ExperimentMetrics:
    """实验指标"""
    accuracy: Optional[float] = None
    loss: Optional[float] = None
    communication_round: Optional[int] = None
    training_time: Optional[float] = None
    pol_verification_time: Optional[float] = None
    blockchain_time: Optional[float] = None
    memory_usage: Optional[float] = None
    cpu_usage: Optional[float] = None
    gpu_usage: Optional[float] = None

@dataclass
class ExperimentStatus:
    """实验状态"""
    experiment_id: str
    name: str
    status: str  # "running", "completed", "failed", "timeout"
    start_time: str
    end_time: Optional[str] = None
    current_round: Optional[int] = None
    total_rounds: Optional[int] = None
    error_message: Optional[str] = None
    metrics: Optional[ExperimentMetrics] = None

class ExperimentMonitor:
    """实验监控器"""
    
    def __init__(self, output_dir: str = None):
        self.output_dir = output_dir or f"experiments/monitor_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.experiments: Dict[str, ExperimentStatus] = {}
        self.system_info = self._collect_system_info()
        self.setup_output_dir()
        self.setup_logging()
    
    def setup_output_dir(self):
        """设置输出目录"""
        Path(self.output_dir).mkdir(parents=True, exist_ok=True)
    
    def setup_logging(self):
        """设置日志"""
        log_file = f"{self.output_dir}/monitor.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(log_file)
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def _collect_system_info(self) -> SystemInfo:
        """收集系统信息"""
        gpu_info = []
        try:
            import torch
            if torch.cuda.is_available():
                for i in range(torch.cuda.device_count()):
                    gpu_info.append({
                        "id": i,
                        "name": torch.cuda.get_device_name(i),
                        "memory_total": torch.cuda.get_device_properties(i).total_memory / 1024**3,  # GB
                    })
        except ImportError:
            pass
        
        return SystemInfo(
            cpu_count=psutil.cpu_count(),
            memory_total=psutil.virtual_memory().total / 1024**3,  # GB
            gpu_info=gpu_info,
            python_version=sys.version,
            timestamp=datetime.now().isoformat()
        )
    
    def start_experiment(self, experiment_id: str, name: str, total_rounds: int) -> str:
        """开始监控实验"""
        status = ExperimentStatus(
            experiment_id=experiment_id,
            name=name,
            status="running",
            start_time=datetime.now().isoformat(),
            total_rounds=total_rounds,
            current_round=0,
            metrics=ExperimentMetrics()
        )
        
        self.experiments[experiment_id] = status
        self._save_status()
        
        self.logger.info(f"🚀 开始监控实验: {name} (ID: {experiment_id})")
        return experiment_id
    
    def update_progress(self, experiment_id: str, current_round: int, metrics: ExperimentMetrics = None):
        """更新实验进度"""
        if experiment_id not in self.experiments:
            self.logger.warning(f"未找到实验: {experiment_id}")
            return
        
        exp = self.experiments[experiment_id]
        exp.current_round = current_round
        if metrics:
            exp.metrics = metrics
        
        # 收集系统资源使用情况
        if exp.metrics:
            exp.metrics.memory_usage = psutil.virtual_memory().percent
            exp.metrics.cpu_usage = psutil.cpu_percent()
            
            # GPU使用率
            try:
                import torch
                if torch.cuda.is_available():
                    exp.metrics.gpu_usage = torch.cuda.utilization()
            except:
                pass
        
        self._save_status()
        
        progress = f"{current_round}/{exp.total_rounds}" if exp.total_rounds else str(current_round)
        self.logger.info(f"📊 实验进度更新: {exp.name} - 轮次 {progress}")
    
    def complete_experiment(self, experiment_id: str, success: bool = True, error_message: str = None):
        """完成实验监控"""
        if experiment_id not in self.experiments:
            self.logger.warning(f"未找到实验: {experiment_id}")
            return
        
        exp = self.experiments[experiment_id]
        exp.status = "completed" if success else "failed"
        exp.end_time = datetime.now().isoformat()
        if error_message:
            exp.error_message = error_message
        
        self._save_status()
        
        status_emoji = "✅" if success else "❌"
        self.logger.info(f"{status_emoji} 实验完成: {exp.name}")
    
    def timeout_experiment(self, experiment_id: str):
        """标记实验超时"""
        if experiment_id not in self.experiments:
            return
        
        exp = self.experiments[experiment_id]
        exp.status = "timeout"
        exp.end_time = datetime.now().isoformat()
        exp.error_message = "实验执行超时"
        
        self._save_status()
        self.logger.warning(f"⏰ 实验超时: {exp.name}")
    
    def _save_status(self):
        """保存状态到文件"""
        status_file = f"{self.output_dir}/experiment_status.json"
        
        data = {
            "system_info": asdict(self.system_info),
            "experiments": {k: asdict(v) for k, v in self.experiments.items()},
            "last_updated": datetime.now().isoformat()
        }
        
        with open(status_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    
    def generate_summary_report(self) -> Dict[str, Any]:
        """生成摘要报告"""
        total_experiments = len(self.experiments)
        completed = sum(1 for exp in self.experiments.values() if exp.status == "completed")
        failed = sum(1 for exp in self.experiments.values() if exp.status == "failed")
        timeout = sum(1 for exp in self.experiments.values() if exp.status == "timeout")
        running = sum(1 for exp in self.experiments.values() if exp.status == "running")
        
        # 计算平均指标
        completed_experiments = [exp for exp in self.experiments.values() if exp.status == "completed"]
        avg_metrics = {}
        if completed_experiments:
            metrics_list = [exp.metrics for exp in completed_experiments if exp.metrics]
            if metrics_list:
                avg_metrics = {
                    "avg_accuracy": sum(m.accuracy for m in metrics_list if m.accuracy) / len([m for m in metrics_list if m.accuracy]) if any(m.accuracy for m in metrics_list) else None,
                    "avg_training_time": sum(m.training_time for m in metrics_list if m.training_time) / len([m for m in metrics_list if m.training_time]) if any(m.training_time for m in metrics_list) else None,
                    "avg_memory_usage": sum(m.memory_usage for m in metrics_list if m.memory_usage) / len([m for m in metrics_list if m.memory_usage]) if any(m.memory_usage for m in metrics_list) else None,
                }
        
        summary = {
            "total_experiments": total_experiments,
            "completed": completed,
            "failed": failed,
            "timeout": timeout,
            "running": running,
            "success_rate": completed / total_experiments if total_experiments > 0 else 0,
            "average_metrics": avg_metrics,
            "system_info": asdict(self.system_info)
        }
        
        # 保存摘要报告
        summary_file = f"{self.output_dir}/summary_report.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        return summary
    
    def print_status(self):
        """打印当前状态"""
        print("\n" + "="*60)
        print("📊 实验监控状态")
        print("="*60)
        
        for exp_id, exp in self.experiments.items():
            status_emoji = {
                "running": "🔄",
                "completed": "✅", 
                "failed": "❌",
                "timeout": "⏰"
            }.get(exp.status, "❓")
            
            progress = ""
            if exp.current_round is not None and exp.total_rounds is not None:
                progress = f" ({exp.current_round}/{exp.total_rounds})"
            
            print(f"{status_emoji} {exp.name}{progress}")
            if exp.error_message:
                print(f"   错误: {exp.error_message}")
        
        print("="*60)

class ExperimentAnalyzer:
    """实验分析器 - 用于离线分析实验结果"""
    
    def __init__(self, status_file: str):
        self.status_file = status_file
        self.data = self._load_data()
    
    def _load_data(self) -> Dict[str, Any]:
        """加载实验数据"""
        with open(self.status_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def analyze_performance(self) -> Dict[str, Any]:
        """分析性能指标"""
        experiments = self.data.get("experiments", {})
        
        # 提取性能数据
        performance_data = []
        for exp_id, exp_data in experiments.items():
            if exp_data.get("status") == "completed" and exp_data.get("metrics"):
                metrics = exp_data["metrics"]
                performance_data.append({
                    "name": exp_data["name"],
                    "accuracy": metrics.get("accuracy"),
                    "training_time": metrics.get("training_time"),
                    "memory_usage": metrics.get("memory_usage"),
                    "cpu_usage": metrics.get("cpu_usage"),
                    "gpu_usage": metrics.get("gpu_usage")
                })
        
        return {
            "experiment_count": len(performance_data),
            "performance_data": performance_data,
            "system_info": self.data.get("system_info")
        }
    
    def generate_analysis_report(self, output_file: str = None):
        """生成分析报告"""
        analysis = self.analyze_performance()
        
        if output_file is None:
            output_file = self.status_file.replace('.json', '_analysis.json')
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, indent=2, ensure_ascii=False)
        
        print(f"📄 分析报告已生成: {output_file}")
        return analysis

def main():
    """命令行工具"""
    import argparse
    
    parser = argparse.ArgumentParser(description="实验监控分析工具")
    parser.add_argument("--analyze", type=str, help="分析指定的状态文件")
    parser.add_argument("--output", type=str, help="输出文件路径")
    
    args = parser.parse_args()
    
    if args.analyze:
        analyzer = ExperimentAnalyzer(args.analyze)
        analyzer.generate_analysis_report(args.output)
    else:
        print("请使用 --analyze 参数指定要分析的状态文件")

if __name__ == "__main__":
    main()
