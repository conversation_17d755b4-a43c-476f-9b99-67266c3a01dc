#!/usr/bin/env python3
"""
优化的实验配置 - 修复了超时和Gas问题
"""

from experiments.parallel_executor import ExperimentConfig

# 基础性能对比实验 (更短的轮次)
BASIC_PERFORMANCE_EXPERIMENTS = [
    ExperimentConfig(
        name="baseline_fashion_mnist_short",
        benchmark="FashionMNIST",
        communication_rounds=2,  # 减少轮次
        enable_pol=False,
        client_num=5,  # 减少客户端数量
        timeout=3600,  # 1小时
    ),
    ExperimentConfig(
        name="pol_fashion_mnist_short",
        benchmark="PoLFashionMNIST",
        communication_rounds=2,
        enable_pol=True,
        client_num=5,
        timeout=3600,
    ),
]

# PoL功能验证实验
POL_VERIFICATION_EXPERIMENTS = [
    ExperimentConfig(
        name="pol_basic_test",
        benchmark="PoLFashionMNIST",
        communication_rounds=1,
        enable_pol=True,
        client_num=3,
        timeout=1800,
    ),
]

# 攻击检测实验 (简化版)
ATTACK_DETECTION_EXPERIMENTS = [
    ExperimentConfig(
        name="attack_free_rider_simple",
        benchmark="PoLFashionMNIST",
        communication_rounds=2,
        enable_pol=True,
        client_num=5,
        attack_type="free_rider",
        malicious_ratio=0.2,
        timeout=3600,
    ),
]

# 实验套件定义
EXPERIMENT_SUITES = {
    "quick": POL_VERIFICATION_EXPERIMENTS,
    "basic": BASIC_PERFORMANCE_EXPERIMENTS,
    "attack": ATTACK_DETECTION_EXPERIMENTS,
    "all": BASIC_PERFORMANCE_EXPERIMENTS + POL_VERIFICATION_EXPERIMENTS + ATTACK_DETECTION_EXPERIMENTS,
}
