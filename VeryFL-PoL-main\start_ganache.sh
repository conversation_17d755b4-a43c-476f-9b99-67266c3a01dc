#!/bin/bash

# 启动Ganache脚本，确保有足够的账户支持50+客户端实验

echo "🚀 启动Ganache区块链网络..."
echo "📊 配置: 100个账户，确定性种子，端口8545"

# 检查Ganache是否已安装
if ! command -v ganache &> /dev/null; then
    echo "❌ Ganache未安装，请先安装:"
    echo "   npm install ganache --global"
    exit 1
fi

# 检查端口8545是否被占用
if curl -s --connect-timeout 2 http://localhost:8545 >/dev/null 2>&1; then
    echo "⚠️  端口8545已被占用，尝试停止现有进程..."
    pkill -f "ganache" || true
    sleep 3
    echo "🔄 已清理现有Ganache进程"
fi

# 启动Ganache，配置100个账户
echo "🔧 启动Ganache..."
# 直接启动Ganache（避免复杂的node参数）
ganache \
    --accounts 100 \
    --deterministic \
    --host 0.0.0.0 \
    --port 8545 \
    --gasLimit ******** \
    --gasPrice *********** \
    --blockTime 1 \
    --verbose &

# 等待Ganache启动
echo "⏳ 等待Ganache启动..."
sleep 5

# 检查Ganache是否成功启动
if curl -s -X POST \
    -H "Content-Type: application/json" \
    -d '{"jsonrpc":"2.0","method":"eth_accounts","params":[],"id":1}' \
    http://localhost:8545 | grep -q "result"; then
    echo "✅ Ganache启动成功！"
    echo "🔗 RPC地址: http://localhost:8545"
    echo "📝 账户数量: 100个"
    echo "💡 现在可以运行实验了"
else
    echo "❌ Ganache启动失败"
    exit 1
fi

# 保持脚本运行，显示日志
echo "📋 Ganache正在运行，按Ctrl+C停止..."
wait
