#!/usr/bin/env python3
"""
在主机上预下载所有数据集
使用方法：python download_datasets.py
"""

import os
import sys
import time
import torch
import torchvision
from torchvision import datasets, transforms
from pathlib import Path

def check_environment():
    """检查环境是否满足要求"""
    print("🔍 检查环境...")
    
    # 检查PyTorch
    print(f"   PyTorch版本: {torch.__version__}")
    print(f"   Torchvision版本: {torchvision.__version__}")
    
    # 检查网络连接
    try:
        import urllib.request
        urllib.request.urlopen('https://www.google.com', timeout=5)
        print("   ✅ 网络连接正常")
    except:
        print("   ⚠️ 网络连接可能有问题，下载可能较慢")
    
    return True

def download_dataset_with_retry(dataset_class, name, data_dir, max_retries=3, **kwargs):
    """带重试机制的数据集下载"""
    
    for attempt in range(max_retries):
        try:
            print(f"   尝试 {attempt + 1}/{max_retries}...")
            
            # 下载训练集
            train_dataset = dataset_class(
                root=str(data_dir),
                train=True,
                download=True,
                transform=transforms.ToTensor(),
                **kwargs
            )
            
            # 下载测试集
            test_dataset = dataset_class(
                root=str(data_dir),
                train=False,
                download=True,
                transform=transforms.ToTensor(),
                **kwargs
            )
            
            return train_dataset, test_dataset
            
        except Exception as e:
            print(f"   ❌ 尝试 {attempt + 1} 失败: {e}")
            if attempt < max_retries - 1:
                wait_time = (attempt + 1) * 10  # 递增等待时间
                print(f"   等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
            else:
                raise e

def download_all_datasets():
    """下载所有需要的数据集"""
    
    print("🚀 VeryFL-PoL 数据集下载器")
    print("=" * 50)
    
    if not check_environment():
        return False
    
    # 创建数据目录
    data_dir = Path("./experiments/data")
    data_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"\n📁 数据保存路径: {data_dir.absolute()}")
    print(f"💾 可用磁盘空间: {get_disk_space(data_dir):.1f} GB")
    
    datasets_info = []
    total_start_time = time.time()
    
    # 1. FashionMNIST (~26MB)
    print("\n" + "="*30)
    print("📥 下载 FashionMNIST (约26MB)...")
    try:
        start_time = time.time()
        train_dataset, test_dataset = download_dataset_with_retry(
            datasets.FashionMNIST, "FashionMNIST", data_dir
        )
        download_time = time.time() - start_time
        datasets_info.append({
            'name': 'FashionMNIST',
            'train_samples': len(train_dataset),
            'test_samples': len(test_dataset),
            'download_time': download_time,
            'status': '✅'
        })
        print(f"   ✅ 完成! 耗时: {download_time:.1f}秒")
        
    except Exception as e:
        print(f"   ❌ FashionMNIST 下载失败: {e}")
        datasets_info.append({
            'name': 'FashionMNIST',
            'status': '❌',
            'error': str(e)
        })
    
    # 2. CIFAR10 (~170MB)
    print("\n" + "="*30)
    print("📥 下载 CIFAR10 (约170MB)...")
    try:
        start_time = time.time()
        train_dataset, test_dataset = download_dataset_with_retry(
            datasets.CIFAR10, "CIFAR10", data_dir
        )
        download_time = time.time() - start_time
        datasets_info.append({
            'name': 'CIFAR10',
            'train_samples': len(train_dataset),
            'test_samples': len(test_dataset),
            'download_time': download_time,
            'status': '✅'
        })
        print(f"   ✅ 完成! 耗时: {download_time:.1f}秒")
        
    except Exception as e:
        print(f"   ❌ CIFAR10 下载失败: {e}")
        datasets_info.append({
            'name': 'CIFAR10',
            'status': '❌',
            'error': str(e)
        })
    
    # 3. CIFAR100 (~170MB)
    print("\n" + "="*30)
    print("📥 下载 CIFAR100 (约170MB)...")
    try:
        start_time = time.time()
        train_dataset, test_dataset = download_dataset_with_retry(
            datasets.CIFAR100, "CIFAR100", data_dir
        )
        download_time = time.time() - start_time
        datasets_info.append({
            'name': 'CIFAR100',
            'train_samples': len(train_dataset),
            'test_samples': len(test_dataset),
            'download_time': download_time,
            'status': '✅'
        })
        print(f"   ✅ 完成! 耗时: {download_time:.1f}秒")
        
    except Exception as e:
        print(f"   ❌ CIFAR100 下载失败: {e}")
        datasets_info.append({
            'name': 'CIFAR100',
            'status': '❌',
            'error': str(e)
        })
    
    # 4. EMNIST (~540MB)
    print("\n" + "="*30)
    print("📥 下载 EMNIST (约540MB)...")
    try:
        start_time = time.time()
        train_dataset, test_dataset = download_dataset_with_retry(
            datasets.EMNIST, "EMNIST", data_dir, split='letters'
        )
        download_time = time.time() - start_time
        datasets_info.append({
            'name': 'EMNIST',
            'train_samples': len(train_dataset),
            'test_samples': len(test_dataset),
            'download_time': download_time,
            'status': '✅'
        })
        print(f"   ✅ 完成! 耗时: {download_time:.1f}秒")
        
    except Exception as e:
        print(f"   ❌ EMNIST 下载失败: {e}")
        datasets_info.append({
            'name': 'EMNIST',
            'status': '❌',
            'error': str(e)
        })
    
    # 显示下载结果
    total_time = time.time() - total_start_time
    print("\n" + "="*50)
    print("📊 下载完成统计:")
    print(f"   总耗时: {total_time:.1f}秒 ({total_time/60:.1f}分钟)")
    
    successful = 0
    for info in datasets_info:
        if info['status'] == '✅':
            print(f"   {info['status']} {info['name']}: {info['train_samples']} 训练样本, {info['test_samples']} 测试样本 ({info['download_time']:.1f}s)")
            successful += 1
        else:
            print(f"   {info['status']} {info['name']}: {info.get('error', '未知错误')}")
    
    print(f"\n成功下载: {successful}/{len(datasets_info)} 个数据集")
    
    # 显示文件大小和目录结构
    show_directory_info(data_dir)
    
    # 生成传输命令
    generate_transfer_commands(data_dir)
    
    return successful == len(datasets_info)

def get_disk_space(path):
    """获取磁盘可用空间(GB)"""
    try:
        import shutil
        total, used, free = shutil.disk_usage(path)
        return free / (1024**3)
    except:
        return 0

def show_directory_info(data_dir):
    """显示目录信息"""
    print(f"\n📁 数据目录信息:")
    
    # 计算总大小
    total_size = 0
    file_count = 0
    for root, dirs, files in os.walk(data_dir):
        for file in files:
            file_path = os.path.join(root, file)
            try:
                size = os.path.getsize(file_path)
                total_size += size
                file_count += 1
            except:
                pass
    
    print(f"   总大小: {total_size / (1024*1024):.1f} MB")
    print(f"   文件数量: {file_count}")
    
    # 显示目录结构（简化版）
    print(f"\n🌳 目录结构:")
    for item in sorted(data_dir.iterdir()):
        if item.is_dir():
            print(f"   📁 {item.name}/")
            # 显示子目录
            try:
                for subitem in sorted(item.iterdir())[:3]:
                    if subitem.is_dir():
                        print(f"      📁 {subitem.name}/")
                    else:
                        size = subitem.stat().st_size / (1024*1024)
                        print(f"      📄 {subitem.name} ({size:.1f}MB)")
            except:
                pass

def generate_transfer_commands(data_dir):
    """生成传输命令"""
    print(f"\n🚀 传输到服务器的命令:")
    print("=" * 30)
    
    print("方法1 - 压缩传输 (推荐):")
    print(f"   cd {data_dir.parent}")
    print(f"   tar -czf datasets.tar.gz data/")
    print(f"   scp datasets.tar.gz username@server_ip:/path/to/VeryFL-PoL-main/experiments/")
    print(f"   # 在服务器上解压:")
    print(f"   # tar -xzf datasets.tar.gz && rm datasets.tar.gz")
    
    print(f"\n方法2 - 直接同步:")
    print(f"   rsync -avz --progress {data_dir}/ username@server_ip:/path/to/VeryFL-PoL-main/experiments/data/")
    
    print(f"\n📋 验证命令 (在服务器上运行):")
    print(f"   cd /path/to/VeryFL-PoL-main")
    print(f"   python verify_datasets.py")

def main():
    """主函数"""
    print("欢迎使用 VeryFL-PoL 数据集下载器!")
    print("这将下载约900MB的数据集文件")
    
    response = input("\n是否继续下载? (y/N): ").lower()
    if response not in ['y', 'yes']:
        print("下载已取消")
        return 0
    
    try:
        success = download_all_datasets()
        
        if success:
            print("\n🎉 所有数据集下载完成!")
            print("现在可以将 experiments/data 目录传输到服务器了")
            return 0
        else:
            print("\n⚠️ 部分数据集下载失败，请检查网络连接后重试")
            return 1
            
    except KeyboardInterrupt:
        print("\n\n⏹️ 下载被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 下载过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
