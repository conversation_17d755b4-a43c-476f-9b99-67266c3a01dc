// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

/**
 * @title SimplePoLManager
 * @dev 简化版PoL管理合约，专注核心功能
 */
contract SimplePoLManager {
    
    // PoL记录结构
    struct PoLRecord {
        bytes32 polHash;        // PoL证明哈希
        uint256 timestamp;      // 提交时间戳
        uint256 totalSteps;     // 训练总步数
        bool isVerified;        // 是否已验证
        bool isValid;           // 验证结果
    }
    
    // 客户端信誉结构
    struct ClientReputation {
        uint256 totalSubmissions;   // 总提交次数
        uint256 validSubmissions;   // 有效提交次数
        uint256 reputationScore;    // 信誉分数 (0-1000)
    }
    
    // 状态变量
    address public owner;
    
    // 存储映射
    mapping(address => PoLRecord[]) public clientPoLRecords;
    mapping(address => ClientReputation) public clientReputations;
    mapping(bytes32 => bool) public usedPoLHashes; // 防止重复提交
    
    // 激励池
    uint256 public totalRewards;
    uint256 public distributedRewards;
    uint256 public currentRound;
    
    // 事件定义
    event PoLSubmitted(
        address indexed client,
        bytes32 indexed polHash,
        uint256 totalSteps,
        uint256 timestamp
    );
    
    event PoLVerified(
        address indexed client,
        bytes32 indexed polHash,
        bool isValid,
        uint256 timestamp
    );
    
    event RewardDistributed(
        address indexed client,
        uint256 amount,
        uint256 round
    );
    
    // 修饰符
    modifier onlyOwner() {
        require(msg.sender == owner, "Only owner can call this function");
        _;
    }
    
    // 构造函数
    constructor() {
        owner = msg.sender;
        currentRound = 1;
    }
    
    /**
     * @dev 客户端提交PoL证明哈希
     */
    function submitPoL(
        bytes32 _polHash,
        string memory _ipfsHash,
        uint256 _totalSteps
    ) external {
        require(_polHash != bytes32(0), "Invalid PoL hash");
        require(!usedPoLHashes[_polHash], "PoL hash already used");
        require(_totalSteps > 0, "Total steps must be positive");
        
        // 创建PoL记录
        PoLRecord memory newRecord = PoLRecord({
            polHash: _polHash,
            timestamp: block.timestamp,
            totalSteps: _totalSteps,
            isVerified: false,
            isValid: false
        });
        
        // 存储记录
        clientPoLRecords[msg.sender].push(newRecord);
        usedPoLHashes[_polHash] = true;
        
        // 更新提交统计
        clientReputations[msg.sender].totalSubmissions++;
        
        emit PoLSubmitted(msg.sender, _polHash, _totalSteps, block.timestamp);
    }
    
    /**
     * @dev 验证PoL证明
     */
    function verifyPoL(
        address _client,
        uint256 _recordIndex,
        bool _isValid
    ) external onlyOwner {
        require(_recordIndex < clientPoLRecords[_client].length, "Invalid record index");
        
        PoLRecord storage record = clientPoLRecords[_client][_recordIndex];
        require(!record.isVerified, "PoL already verified");
        
        // 更新验证状态
        record.isVerified = true;
        record.isValid = _isValid;
        
        // 更新客户端信誉
        ClientReputation storage reputation = clientReputations[_client];
        
        if (_isValid) {
            reputation.validSubmissions++;
            // 有效提交增加信誉
            if (reputation.reputationScore < 1000) {
                reputation.reputationScore += 50;
                if (reputation.reputationScore > 1000) {
                    reputation.reputationScore = 1000;
                }
            }
        } else {
            // 无效提交减少信誉
            if (reputation.reputationScore > 50) {
                reputation.reputationScore -= 50;
            } else {
                reputation.reputationScore = 0;
            }
        }
        
        emit PoLVerified(_client, record.polHash, _isValid, block.timestamp);
    }
    
    /**
     * @dev 分配激励奖励
     */
    function distributeRewards(address[] memory _clients, uint256[] memory _rewards) 
        external onlyOwner {
        require(_clients.length == _rewards.length, "Arrays length mismatch");
        
        uint256 totalDistribution = 0;
        for (uint256 i = 0; i < _rewards.length; i++) {
            totalDistribution += _rewards[i];
        }
        
        require(
            distributedRewards + totalDistribution <= totalRewards,
            "Insufficient reward pool"
        );
        
        // 分配奖励
        for (uint256 i = 0; i < _clients.length; i++) {
            if (_rewards[i] > 0) {
                distributedRewards += _rewards[i];
                emit RewardDistributed(_clients[i], _rewards[i], currentRound);
            }
        }
    }
    
    /**
     * @dev 添加奖励到激励池
     */
    function addToIncentivePool() external payable onlyOwner {
        totalRewards += msg.value;
    }
    
    /**
     * @dev 开始新一轮
     */
    function startNewRound() external onlyOwner {
        currentRound++;
    }
    
    /**
     * @dev 获取客户端PoL记录数量
     */
    function getClientPoLCount(address _client) external view returns (uint256) {
        return clientPoLRecords[_client].length;
    }
    
    /**
     * @dev 获取客户端信誉信息
     */
    function getClientReputation(address _client) external view returns (
        uint256 totalSubmissions,
        uint256 validSubmissions,
        uint256 reputationScore
    ) {
        ClientReputation memory reputation = clientReputations[_client];
        return (
            reputation.totalSubmissions,
            reputation.validSubmissions,
            reputation.reputationScore
        );
    }
    
    /**
     * @dev 获取激励池信息
     */
    function getIncentivePoolInfo() external view returns (
        uint256 _totalRewards,
        uint256 _distributedRewards,
        uint256 _currentRound
    ) {
        return (totalRewards, distributedRewards, currentRound);
    }
}
