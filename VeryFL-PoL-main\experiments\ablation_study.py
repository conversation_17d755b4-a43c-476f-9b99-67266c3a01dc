#!/usr/bin/env python3
"""
消融实验框架
系统性地分析各个组件对系统性能的贡献
"""

import os
import sys
import json
import time
import logging
import argparse
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Tuple
from dataclasses import dataclass
import itertools

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from experiments.experiment_framework import ExperimentFramework, ExperimentConfig, ExperimentResult
from experiments.statistical_analyzer import StatisticalAnalyzer

@dataclass
class AblationConfig:
    """消融实验配置"""
    name: str
    enable_pol: bool
    enable_compression: bool
    enable_blockchain: bool
    enable_incentives: bool
    pol_save_freq: int
    pol_verification_ratio: float
    description: str

class AblationStudy:
    """消融实验主类"""
    
    def __init__(self, output_dir: str = "./experiments/ablation_results"):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(f'{output_dir}/ablation_study.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # 初始化实验框架和统计分析器
        self.experiment_framework = ExperimentFramework(output_dir)
        self.statistical_analyzer = StatisticalAnalyzer()
    
    def define_ablation_configs(self) -> List[AblationConfig]:
        """定义消融实验配置"""
        configs = [
            # 基线：所有功能都关闭
            AblationConfig(
                name="Baseline",
                enable_pol=False,
                enable_compression=False,
                enable_blockchain=False,
                enable_incentives=False,
                pol_save_freq=None,  # 优化：自动设置为每epoch一次
                pol_verification_ratio=0.1,
                description="基线系统（原版VeryFL）"
            ),
            
            # 只启用PoL
            AblationConfig(
                name="PoL_Only",
                enable_pol=True,
                enable_compression=False,
                enable_blockchain=False,
                enable_incentives=False,
                pol_save_freq=None,  # 优化：自动设置为每epoch一次
                pol_verification_ratio=0.1,
                description="仅启用学习证明（已优化）"
            ),
            
            # PoL + 压缩
            AblationConfig(
                name="PoL_Compression",
                enable_pol=True,
                enable_compression=True,
                enable_blockchain=False,
                enable_incentives=False,
                pol_save_freq=None,  # 优化：自动设置为每epoch一次
                pol_verification_ratio=0.1,
                description="学习证明 + 压缩（已优化）"
            ),
            
            # PoL + 区块链
            AblationConfig(
                name="PoL_Blockchain",
                enable_pol=True,
                enable_compression=False,
                enable_blockchain=True,
                enable_incentives=False,
                pol_save_freq=None,  # 优化：自动设置为每epoch一次
                pol_verification_ratio=0.1,
                description="学习证明 + 区块链（已优化）"
            ),
            
            # PoL + 激励
            AblationConfig(
                name="PoL_Incentives",
                enable_pol=True,
                enable_compression=False,
                enable_blockchain=True,
                enable_incentives=True,
                pol_save_freq=None,  # 优化：自动设置为每epoch一次
                pol_verification_ratio=0.1,
                description="学习证明 + 激励机制（已优化）"
            ),
            
            # 完整系统
            AblationConfig(
                name="Full_System",
                enable_pol=True,
                enable_compression=True,
                enable_blockchain=True,
                enable_incentives=True,
                pol_save_freq=None,  # 优化：自动设置为每epoch一次
                pol_verification_ratio=0.1,
                description="完整系统（所有功能，已优化）"
            )
        ]
        
        return configs
    
    def run_component_ablation(self,
                              dataset: str = "FashionMNIST",
                              client_num: int = 10,
                              communication_rounds: int = 10,
                              batch_size: int = 32,
                              learning_rate: float = 0.01,
                              local_epochs: int = 5) -> List[ExperimentResult]:
        """运行组件消融实验"""
        self.logger.info("开始组件消融实验")
        
        configs = self.define_ablation_configs()
        results = []
        
        for config in configs:
            self.logger.info(f"运行配置: {config.name} - {config.description}")
            
            # 创建实验配置
            exp_config = ExperimentConfig(
                name=f"Ablation_{config.name}_{dataset}",
                dataset=dataset,
                model="simpleCNN" if dataset == "FashionMNIST" else ("resnet18" if dataset == "CIFAR10" else "resnet34"),
                client_num=client_num,
                communication_round=communication_rounds,
                enable_pol=config.enable_pol,
                enable_compression=config.enable_compression,
                enable_blockchain=config.enable_blockchain,
                pol_save_freq=config.pol_save_freq,
                pol_verification_ratio=config.pol_verification_ratio,
                batch_size=batch_size,
                learning_rate=learning_rate,
                local_epochs=local_epochs
            )
            
            # 运行实验
            try:
                result = self.experiment_framework.run_single_experiment(exp_config)
                if result is None:
                    self.logger.error(f"实验失败: {config.name}, 返回None")
                    # 创建一个失败的结果
                    result = ExperimentResult(
                        config=exp_config,
                        final_accuracy=0.0,
                        convergence_rounds=communication_rounds,
                        total_time=0.0,
                        communication_cost=0.0,
                        storage_cost=0.0
                    )
                result.config.name = config.name  # 使用简化名称
                results.append(result)
            except Exception as e:
                self.logger.error(f"实验失败: {config.name}, 错误: {e}")
                # 创建一个失败的结果
                result = ExperimentResult(
                    config=exp_config,
                    final_accuracy=0.0,
                    convergence_rounds=communication_rounds,
                    total_time=0.0,
                    communication_cost=0.0,
                    storage_cost=0.0
                )
                result.config.name = config.name
                results.append(result)
        
        return results
    
    def run_parameter_sensitivity(self,
                                dataset: str = "FashionMNIST",
                                client_num: int = 10,
                                communication_rounds: int = 10) -> List[ExperimentResult]:
        """运行参数敏感性分析"""
        self.logger.info("开始参数敏感性分析")
        
        # 定义参数范围（已优化：使用更合理的范围）
        pol_save_freqs = [None]  # 使用自动设置（每epoch一次）
        pol_verification_ratios = [0.1]  # 对应Q=1的最优设置
        
        results = []
        
        for save_freq in pol_save_freqs:
            for verify_ratio in pol_verification_ratios:
                config_name = f"Param_freq{save_freq}_ratio{verify_ratio}"
                self.logger.info(f"运行参数配置: {config_name}")
                
                exp_config = ExperimentConfig(
                    name=config_name,
                    dataset=dataset,
                    model="simpleCNN" if dataset == "FashionMNIST" else ("resnet18" if dataset == "CIFAR10" else "resnet34"),
                    client_num=client_num,
                    communication_round=communication_rounds,
                    enable_pol=True,
                    enable_compression=True,
                    enable_blockchain=True,
                    pol_save_freq=save_freq,
                    pol_verification_ratio=verify_ratio,
                    batch_size=32,
                    learning_rate=0.01,
                    local_epochs=5
                )
                
                result = self.experiment_framework.run_single_experiment(exp_config)
                results.append(result)
        
        return results
    
    def analyze_component_contribution(self, results: List[ExperimentResult]) -> Dict[str, Any]:
        """分析各组件的贡献"""
        self.logger.info("分析组件贡献")

        if not results:
            return {
                'baseline_performance': {'accuracy': 0.0, 'time': 0.0},
                'component_contributions': {}
            }

        # 转换为DataFrame
        data = []
        for result in results:
            data.append({
                'config_name': result.config.name,
                'final_accuracy': result.final_accuracy,
                'total_time': result.total_time,
                'communication_cost': result.communication_cost,
                'storage_cost': result.storage_cost
            })
        
        df = pd.DataFrame(data)
        
        # 计算相对于基线的改进
        baseline_row = df[df['config_name'] == 'Baseline'].iloc[0]
        
        analysis = {
            'baseline_performance': {
                'accuracy': baseline_row['final_accuracy'],
                'time': baseline_row['total_time'],
                'communication_cost': baseline_row['communication_cost']
            },
            'component_contributions': {}
        }
        
        # 分析每个组件的贡献
        component_configs = {
            'PoL': 'PoL_Only',
            'Compression': 'PoL_Compression',
            'Blockchain': 'PoL_Blockchain',
            'Incentives': 'PoL_Incentives',
            'Full_System': 'Full_System'
        }
        
        for component, config_name in component_configs.items():
            if config_name in df['config_name'].values:
                row = df[df['config_name'] == config_name].iloc[0]
                
                accuracy_improvement = row['final_accuracy'] - baseline_row['final_accuracy']
                time_overhead = (row['total_time'] - baseline_row['total_time']) / baseline_row['total_time'] * 100
                
                analysis['component_contributions'][component] = {
                    'accuracy_improvement': accuracy_improvement,
                    'time_overhead_percent': time_overhead,
                    'absolute_accuracy': row['final_accuracy'],
                    'absolute_time': row['total_time']
                }
        
        return analysis
    
    def generate_ablation_report(self, component_results: List[ExperimentResult],
                                parameter_results: List[ExperimentResult] = None) -> str:
        """生成消融实验报告"""
        report_lines = []
        report_lines.append("# 消融实验报告\n\n")
        
        # 组件分析
        report_lines.append("## 1. 组件贡献分析\n\n")
        
        component_analysis = self.analyze_component_contribution(component_results)
        baseline = component_analysis['baseline_performance']
        
        report_lines.append(f"### 基线性能\n")
        report_lines.append(f"- 准确率: {baseline['accuracy']:.4f}\n")
        report_lines.append(f"- 训练时间: {baseline['time']:.2f}秒\n\n")
        
        report_lines.append("### 各组件贡献\n")
        for component, contrib in component_analysis['component_contributions'].items():
            report_lines.append(f"#### {component}\n")
            report_lines.append(f"- 准确率提升: {contrib['accuracy_improvement']:+.4f}\n")
            report_lines.append(f"- 时间开销: {contrib['time_overhead_percent']:+.2f}%\n")
            report_lines.append(f"- 绝对准确率: {contrib['absolute_accuracy']:.4f}\n\n")
        
        # 参数敏感性分析
        if parameter_results:
            report_lines.append("## 2. 参数敏感性分析\n\n")
            
            # 转换为DataFrame进行分析
            param_data = []
            for result in parameter_results:
                # 从名称中提取参数
                name_parts = result.config.name.split('_')
                save_freq = int(name_parts[1].replace('freq', ''))
                verify_ratio = float(name_parts[2].replace('ratio', ''))
                
                param_data.append({
                    'save_freq': save_freq,
                    'verify_ratio': verify_ratio,
                    'accuracy': result.final_accuracy,
                    'time': result.total_time
                })
            
            param_df = pd.DataFrame(param_data)
            
            # 分析保存频率的影响
            freq_analysis = param_df.groupby('save_freq')['accuracy'].agg(['mean', 'std']).round(4)
            report_lines.append("### PoL保存频率影响\n")
            for freq, row in freq_analysis.iterrows():
                report_lines.append(f"- 频率{freq}: {row['mean']:.4f} ± {row['std']:.4f}\n")
            report_lines.append("\n")
            
            # 分析验证比例的影响
            ratio_analysis = param_df.groupby('verify_ratio')['accuracy'].agg(['mean', 'std']).round(4)
            report_lines.append("### PoL验证比例影响\n")
            for ratio, row in ratio_analysis.iterrows():
                report_lines.append(f"- 比例{ratio}: {row['mean']:.4f} ± {row['std']:.4f}\n")
            report_lines.append("\n")
        
        # 统计分析
        if len(component_results) >= 2:
            report_lines.append("## 3. 统计显著性分析\n\n")
            
            # 比较基线和完整系统
            baseline_result = next((r for r in component_results if r.config.name == 'Baseline'), None)
            full_result = next((r for r in component_results if r.config.name == 'Full_System'), None)
            
            if baseline_result and full_result:
                # 这里简化处理，实际应该有多次重复实验的数据
                report_lines.append("### 基线 vs 完整系统\n")
                accuracy_diff = full_result.final_accuracy - baseline_result.final_accuracy
                time_diff = full_result.total_time - baseline_result.total_time
                
                report_lines.append(f"- 准确率差异: {accuracy_diff:+.4f}\n")
                report_lines.append(f"- 时间差异: {time_diff:+.2f}秒\n")
                report_lines.append("- 注：需要多次重复实验进行完整的统计检验\n\n")
        
        return "".join(report_lines)
    
    def save_results(self, component_results: List[ExperimentResult],
                    parameter_results: List[ExperimentResult] = None):
        """保存消融实验结果"""
        # 保存组件消融结果
        component_data = []
        if component_results:
            for result in component_results:
                component_data.append({
                    'config_name': result.config.name,
                    'final_accuracy': result.final_accuracy,
                    'convergence_rounds': result.convergence_rounds,
                    'total_time': result.total_time,
                    'communication_cost': result.communication_cost,
                    'storage_cost': result.storage_cost
                })
        
        component_df = pd.DataFrame(component_data)
        component_df.to_csv(os.path.join(self.output_dir, "component_ablation.csv"), index=False)
        
        # 保存参数敏感性结果
        if parameter_results:
            param_data = []
            for result in parameter_results:
                name_parts = result.config.name.split('_')
                save_freq = int(name_parts[1].replace('freq', ''))
                verify_ratio = float(name_parts[2].replace('ratio', ''))
                
                param_data.append({
                    'save_freq': save_freq,
                    'verify_ratio': verify_ratio,
                    'final_accuracy': result.final_accuracy,
                    'total_time': result.total_time,
                    'communication_cost': result.communication_cost
                })
            
            param_df = pd.DataFrame(param_data)
            param_df.to_csv(os.path.join(self.output_dir, "parameter_sensitivity.csv"), index=False)
        
        # 生成报告
        report = self.generate_ablation_report(component_results, parameter_results)
        with open(os.path.join(self.output_dir, "ablation_report.md"), 'w', encoding='utf-8') as f:
            f.write(report)
        
        self.logger.info(f"消融实验结果已保存到: {self.output_dir}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='VeryFL-PoL 消融实验')
    
    parser.add_argument('--experiment', choices=['component', 'parameter', 'all'],
                       default='all', help='实验类型')
    parser.add_argument('--dataset', choices=['FashionMNIST', 'CIFAR10', 'CIFAR100'],
                       default='FashionMNIST', help='数据集')
    parser.add_argument('--client-num', type=int, default=10, help='客户端数量')
    parser.add_argument('--communication-rounds', type=int, default=5, help='通信轮数')
    parser.add_argument('--output-dir', default='./ablation_results', help='输出目录')
    
    args = parser.parse_args()
    
    # 创建消融实验
    ablation = AblationStudy(output_dir=args.output_dir)
    
    # 创建logger用于记录信息
    import logging
    logger = logging.getLogger(__name__)

    logger.info("🔬 VeryFL-PoL 消融实验")
    logger.info("=" * 50)
    logger.info(f"📁 输出目录: {args.output_dir}")
    logger.info(f"🔬 实验类型: {args.experiment}")
    logger.info(f"📊 数据集: {args.dataset}")
    logger.info("=" * 50)
    
    component_results = None
    parameter_results = None
    
    if args.experiment in ['component', 'all']:
        logger.info("1. 运行组件消融实验...")
        component_results = ablation.run_component_ablation(
            dataset=args.dataset,
            client_num=args.client_num,
            communication_rounds=args.communication_rounds
        )
    
    if args.experiment in ['parameter', 'all']:
        logger.info("2. 运行参数敏感性分析...")
        parameter_results = ablation.run_parameter_sensitivity(
            dataset=args.dataset,
            client_num=args.client_num,
            communication_rounds=args.communication_rounds
        )
    
    # 保存结果
    ablation.save_results(component_results, parameter_results)
    
    logger.info("✅ 消融实验完成！")
    logger.info(f"📊 结果保存在: {ablation.output_dir}")

if __name__ == "__main__":
    main()
