# VeryFL-PoL 实验系统使用指南

## 🚀 快速开始

### 1. 启动区块链网络
```bash
cd VeryFL-PoL-main
./start_ganache.sh
```

### 2. 运行单个实验
```bash
cd VeryFL-PoL-main
python task.py --benchmark PoLFashionMNIST --communication-rounds 2 --enable-pol
```

### 3. 并行运行多个实验
```bash
cd VeryFL-PoL-main/experiments
python parallel_executor.py --suite quick --max-parallel 2
```

## 📊 并行执行器

### 使用方法
```bash
python parallel_executor.py [选项]
```

### 选项说明
- `--suite {quick,basic,full}`: 选择实验套件
  - `quick`: 2个快速实验 (1-2轮通信)
  - `basic`: 3个基础实验 (2-3轮通信)
  - `full`: 4个完整实验 (5轮通信)
- `--max-parallel N`: 最大并行实验数 (默认: 2)
- `--output-dir DIR`: 指定输出目录

### 实验套件详情

#### Quick套件 (测试用)
- `pol_fashion_mnist_1`: PoL + FashionMNIST, 1轮通信
- `pol_fashion_mnist_2`: PoL + FashionMNIST, 2轮通信

#### Basic套件 (基础对比)
- `pol_fashion_mnist_3`: PoL + FashionMNIST, 3轮通信
- `pol_cifar10_2`: PoL + CIFAR10, 2轮通信
- `baseline_fashion_mnist`: 基线 + FashionMNIST, 2轮通信

#### Full套件 (完整评估)
- `pol_fashion_mnist_5`: PoL + FashionMNIST, 5轮通信
- `pol_cifar10_5`: PoL + CIFAR10, 5轮通信
- `baseline_fashion_mnist_5`: 基线 + FashionMNIST, 5轮通信
- `baseline_cifar10_5`: 基线 + CIFAR10, 5轮通信

## 📈 实验监控系统

### 自动监控
实验运行时会自动生成监控数据，包括：
- 系统资源使用情况 (CPU, 内存, GPU)
- 实验进度和状态
- 性能指标 (准确率, 训练时间等)

### 监控文件位置
- 并行执行器: `experiments/parallel_results_YYYYMMDD_HHMMSS/`
- 单独实验: `experiments/monitor_YYYYMMDD_HHMMSS/`

### 分析监控数据
```bash
python experiment_monitor.py --analyze path/to/experiment_status.json
```

## 📁 输出文件结构

### 并行执行器输出
```
parallel_results_YYYYMMDD_HHMMSS/
├── executor.log                    # 执行器日志
├── experiment_report.json          # 实验报告
├── experiment_name_1/
│   └── experiment.log              # 实验1日志
└── experiment_name_2/
    └── experiment.log              # 实验2日志
```

### 监控系统输出
```
monitor_YYYYMMDD_HHMMSS/
├── monitor.log                     # 监控日志
├── experiment_status.json          # 实验状态
└── summary_report.json             # 摘要报告
```

### 实验报告格式
```json
{
  "summary": {
    "total_experiments": 2,
    "successful_experiments": 2,
    "failed_experiments": 0,
    "success_rate": 1.0,
    "total_duration": 15.47,
    "average_duration": 7.74
  },
  "experiments": [
    {
      "config": {
        "name": "pol_fashion_mnist_1",
        "benchmark": "PoLFashionMNIST",
        "communication_rounds": 1,
        "enable_pol": true
      },
      "success": true,
      "start_time": "2025-07-28 15:29:20",
      "end_time": "2025-07-28 15:29:28",
      "duration": 7.99,
      "log_file": "path/to/experiment.log"
    }
  ]
}
```

## 🔧 系统特性

### 自动设备检测
- 自动检测GPU/CPU环境
- 优先使用GPU，无GPU时自动切换CPU
- 支持多GPU环境

### PoL优化
- 严格按照论文设置: k=S (每epoch保存检查点)
- 验证预算: Q=1 (每epoch验证1个最大更新)
- 优化深拷贝操作，减少内存开销
- 30%超时缓冲，确保实验稳定性

### 容错机制
- 实验超时自动处理
- 异常情况详细记录
- 部分实验失败不影响其他实验

## 🚨 注意事项

### 运行前准备
1. 确保Ganache区块链网络已启动
2. 检查系统资源 (内存 > 8GB 推荐)
3. 确保Python环境包含所需依赖

### 性能建议
- 并行度建议: CPU核心数的50-75%
- 内存使用: 每个并行实验约需2-4GB内存
- GPU环境下性能显著提升

### 故障排除
1. **实验卡住**: 检查Ganache是否运行
2. **内存不足**: 减少并行度
3. **GPU错误**: 系统会自动切换到CPU

## 📞 技术支持

如遇问题，请检查：
1. 实验日志文件 (`experiment.log`)
2. 执行器日志 (`executor.log`)
3. 监控状态文件 (`experiment_status.json`)

这些文件包含了完整的实验执行信息，便于问题诊断和性能分析。
