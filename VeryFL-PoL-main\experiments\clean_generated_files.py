#!/usr/bin/env python3
'''
实验文件清理脚本
清理experiments目录下的生成文件，保留实验脚本
'''

import os
import shutil
from pathlib import Path

def clean_experiments():
    '''清理experiments目录下的生成文件'''
    base_dir = Path("experiments")
    
    # 需要清理的目录
    clean_dirs = [
        "data", "log", "pol_data", "results", "models", 
        "monitor", "temp"
    ]
    
    # 清理匹配模式的目录
    patterns = [
        "parallel_results*",
        "sota_results*", 
        "compression_results*",
        "network_results*",
        "ablation_results*",
        "monitor_*"
    ]
    
    cleaned_count = 0
    
    # 清理固定目录
    for dir_name in clean_dirs:
        dir_path = base_dir / dir_name
        if dir_path.exists():
            shutil.rmtree(dir_path)
            dir_path.mkdir(exist_ok=True)  # 重新创建空目录
            print(f"🧹 清理: experiments/{dir_name}")
            cleaned_count += 1
    
    # 清理匹配模式的目录
    for pattern in patterns:
        for path in base_dir.glob(pattern):
            if path.is_dir():
                shutil.rmtree(path)
                print(f"🧹 清理: {path}")
                cleaned_count += 1
    
    print(f"✅ 清理完成，共清理 {cleaned_count} 个目录")

if __name__ == "__main__":
    print("🧹 开始清理实验生成文件...")
    clean_experiments()
    print("🎉 清理完成！")
