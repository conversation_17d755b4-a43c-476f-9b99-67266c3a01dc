#!/usr/bin/env python3
"""
检查实验生成的文件数量和大小
用于监控文件生成情况，避免产生过多文件
"""

import os
import sys
from pathlib import Path

def check_file_count(directory="./experiments"):
    """检查指定目录下的文件数量和大小"""
    if not os.path.exists(directory):
        print(f"目录不存在: {directory}")
        return
    
    print(f"🔍 检查目录: {directory}")
    print("=" * 60)
    
    # 统计不同类型的文件
    file_stats = {
        '.pkl': {'count': 0, 'size': 0},
        '.csv': {'count': 0, 'size': 0},
        '.json': {'count': 0, 'size': 0},
        '.png': {'count': 0, 'size': 0},
        '.log': {'count': 0, 'size': 0},
        '.md': {'count': 0, 'size': 0},
        'other': {'count': 0, 'size': 0}
    }
    
    total_files = 0
    total_size = 0
    
    # 遍历所有文件
    for root, dirs, files in os.walk(directory):
        for file in files:
            file_path = os.path.join(root, file)
            if os.path.isfile(file_path):
                file_size = os.path.getsize(file_path)
                total_files += 1
                total_size += file_size
                
                # 按扩展名分类
                ext = Path(file).suffix.lower()
                if ext in file_stats:
                    file_stats[ext]['count'] += 1
                    file_stats[ext]['size'] += file_size
                else:
                    file_stats['other']['count'] += 1
                    file_stats['other']['size'] += file_size
    
    # 显示统计结果
    print(f"📊 文件统计:")
    print(f"总文件数: {total_files}")
    print(f"总大小: {format_size(total_size)}")
    print()
    
    print("📋 按类型分类:")
    for ext, stats in file_stats.items():
        if stats['count'] > 0:
            print(f"  {ext:8}: {stats['count']:4}个文件, {format_size(stats['size'])}")
    
    # 警告检查
    print("\n⚠️ 警告检查:")
    if file_stats['.pkl']['count'] > 50:
        print(f"🚨 .pkl文件过多: {file_stats['.pkl']['count']}个 (建议<50个)")
    
    if total_size > 100 * 1024 * 1024:  # 100MB
        print(f"🚨 总文件大小过大: {format_size(total_size)} (建议<100MB)")
    
    if total_files > 100:
        print(f"🚨 文件数量过多: {total_files}个 (建议<100个)")
    
    if file_stats['.pkl']['count'] == 0 and file_stats['.csv']['count'] == 0:
        print("✅ 文件数量控制良好，没有大量数据文件")

def format_size(size_bytes):
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"

def main():
    """主函数"""
    print("🔍 VeryFL-PoL 文件数量检查工具")
    print("=" * 60)
    
    # 检查实验目录
    check_file_count("./experiments")
    
    print("\n" + "=" * 60)
    print("💡 优化建议:")
    print("- 如果.pkl文件过多，请启用 enable_disk_save=False")
    print("- 如果总大小过大，请清理过时的实验结果")
    print("- 定期运行此脚本监控文件生成情况")

if __name__ == "__main__":
    main()
