import logging
import threading
import time
import os
import torch
import uuid
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed, TimeoutError

# 修复OpenMP并行冲突问题
os.environ['OMP_NUM_THREADS'] = '2'
os.environ['MKL_NUM_THREADS'] = '2'
os.environ['NUMEXPR_NUM_THREADS'] = '2'

logger = logging.getLogger(__name__)

from torch.utils.data import DataLoader

# 导入实验监控系统
try:
    from experiments.experiment_monitor import ExperimentMonitor, ExperimentMetrics
    HAS_MONITOR = True
except ImportError:
    HAS_MONITOR = False
    logger.warning("实验监控系统不可用")

from config.algorithm import Algorithm
from server.aggregation_alg.fedavg import fedavgAggregator
from client.clients import Client, BaseClient, SignClient
from client.polClient import PoLClient, PoLSignClient
from client.trainer.fedproxTrainer import fedproxTrainer
from client.trainer.SignTrainer import SignTrainer
from client.trainer.polTrainer import <PERSON><PERSON>rainer, PoLFedProxTrainer
from model.ModelFactory import ModelFactory
from dataset.DatasetFactory import DatasetFactory
from dataset.DatasetSpliter import DatasetSpliter
from chainfl.interact import chain_proxy


class Task:
    '''
    WorkFlow of a Task:
    0. Construct (Model, Dataset)--> Benchmark
    1. Construct (Server, Client)--> FL Algorithm
    3. Process of the dataset 
    '''
    def __init__(self, global_args: dict, train_args: dict, algorithm: Algorithm):
        self.global_args = global_args
        self.train_args = train_args
        self.model = None

        # 自动检测GPU/CPU设备
        self.device = self._detect_device()
        global_args['device'] = self.device

        # 初始化实验监控
        self.experiment_id = str(uuid.uuid4())
        self.monitor = None
        if HAS_MONITOR:
            self.monitor = ExperimentMonitor()
            benchmark = global_args.get('benchmark', 'unknown')
            rounds = global_args.get('communication_rounds', 1)
            self.monitor.start_experiment(self.experiment_id, benchmark, rounds)

        # PoL相关配置（支持新旧参数兼容）
        self.enable_pol = global_args.get('enable_pol', False)

        # 兼容旧参数名：将verification_ratio转换为verification_budget_Q
        verification_ratio = global_args.get('pol_verification_ratio', 0.1)
        verification_budget_Q = global_args.get('verification_budget_Q', 1)

        # 如果只提供了verification_ratio，转换为verification_budget_Q
        if 'pol_verification_ratio' in global_args and 'verification_budget_Q' not in global_args:
            # 按PoL论文建议，Q=1通常就足够了
            verification_budget_Q = 1  # 论文建议的最优值

        # 处理pol_save_freq参数：None表示自动设置为每epoch一次
        pol_save_freq = global_args.get('pol_save_freq')
        if pol_save_freq is None:
            # None表示使用自动设置（每epoch一次）
            save_freq = None
        else:
            # 使用指定的频率
            save_freq = pol_save_freq

        self.pol_config = {
            'enable_pol': self.enable_pol,
            'save_freq': save_freq,  # 修复：正确传递None值以触发自动设置
            'verification_budget_Q': verification_budget_Q,  # 新参数
            'verification_ratio': verification_ratio,        # 保留旧参数以兼容
            'require_pol': global_args.get('require_pol', True),
            'enable_blockchain': global_args.get('enable_blockchain', True),
            'enable_incentives': global_args.get('enable_incentives', True),
            'enable_compression': global_args.get('enable_compression', True),
            'save_dir': './experiments/pol_data'  # 修复：PoL数据保存到experiments目录下
        }
        
        #Get Dataset
        logger.info("Constructing dataset %s from dataset Factory", global_args.get('dataset'))
        self.train_dataset = DatasetFactory().get_dataset(global_args.get('dataset'),True)
        self.test_dataset =  DatasetFactory().get_dataset(global_args.get('dataset'),False)
        #Get Model
        logger.info("Constructing Model from model factory with model %s and class_num %d", global_args['model'], global_args['class_num'])
        self.model = ModelFactory().get_model(model=self.global_args.get('model'),class_num=self.global_args.get('class_num'))
        
        #FL alg
        logger.info(f"Algorithm: {algorithm}")
        self.server = algorithm.get_server()
        # 如果是PoL聚合器，传递PoL配置
        if hasattr(self.server, '__name__') and 'PoL' in self.server.__name__:
            self.server = self.server(pol_config=self.pol_config)
        else:
            self.server = self.server()
        self.trainer = algorithm.get_trainer()
        self.client = algorithm.get_client()
        
        #Get Client and Trainer
        self.client_list = None
        self.client_pool : list[Client] = []
        
    def __repr__(self) -> str:
        pass
    
    def _construct_dataloader(self):
        logger.info("Constructing dataloader with batch size %d, client_num: %d, non-iid: %s", self.global_args.get('batch_size')
                    , chain_proxy.get_client_num(), "True" if self.global_args['non-iid'] else "False")
        batch_size = self.global_args.get('batch_size')
        batch_size = 8 if (batch_size is None) else batch_size
        self.train_dataloader_list = DatasetSpliter().random_split(dataset     = self.train_dataset,
                                                                   client_list = chain_proxy.get_client_list(),
                                                                   batch_size  = batch_size)
        self.test_dataloader = DataLoader(dataset=self.test_dataset, batch_size=batch_size, shuffle=True)
    
    def _construct_sign(self):
        self.keys_dict = dict()
        self.keys = list()
        sign_num = self.global_args.get('sign_num')
        if(None == sign_num):
            sign_num = 0
            logger.info("No client need to add watermark")
            # 修复：确保client_list是字典而不是列表
            if isinstance(self.client_list, dict):
                for ind, (client_id,_) in enumerate(self.client_list.items()):
                    self.keys_dict[client_id] = None
            else:
                # 如果client_list是列表，转换为字典格式
                for ind, client_id in enumerate(self.client_list):
                    self.keys_dict[client_id] = None
        else:
            logger.info(f"{sign_num} client(s) will inject watermark into their models")

            for i in range(self.global_args.get('client_num')):
                if i < self.global_args.get('sign_num'):
                    key = chain_proxy.construct_sign(self.global_args)
                    self.keys.append(key)
                else :
                    self.keys.append(None)

            # 修复：确保client_list是字典而不是列表
            if isinstance(self.client_list, dict):
                for ind, (client_id,_) in enumerate(self.client_list.items()):
                    self.keys_dict[client_id] = self.keys[ind]
            else:
                # 如果client_list是列表，转换为字典格式
                for ind, client_id in enumerate(self.client_list):
                    self.keys_dict[client_id] = self.keys[ind]

            #Project the watermark to the client and work with the blockchain
            #Get model with watermark integration
            tmp_args = chain_proxy.construct_sign(self.global_args)
            self.model = ModelFactory().get_sign_model(model          = self.global_args.get('model'),
                                                       class_num      = self.global_args.get('class_num'),
                                                       in_channels    = self.global_args.get('in_channels'),
                                                       watermark_args = tmp_args)
        return
    
    def _regist_client(self):
        #Regist the client to the blockchain.
        for i in range(self.global_args['client_num']):
            chain_proxy.client_regist()
        self.client_list = chain_proxy.get_client_list()
    
    def _construct_client(self):
        # 修复：确保client_list是字典而不是列表
        if isinstance(self.client_list, dict):
            client_items = self.client_list.items()
        else:
            # 如果client_list是列表，转换为字典格式
            client_items = [(client_id, None) for client_id in self.client_list]

        for client_id, _ in client_items:
            # 检查是否是PoL客户端
            if hasattr(self.client, '__name__') and 'PoL' in self.client.__name__:
                new_client = self.client(
                    client_id,
                    self.train_dataloader_list[client_id],
                    self.model,
                    self.trainer,
                    self.train_args,
                    self.test_dataloader,
                    self.keys_dict[client_id],
                    pol_config=self.pol_config
                )
            else:
                new_client = self.client(
                    client_id,
                    self.train_dataloader_list[client_id],
                    self.model,
                    self.trainer,
                    self.train_args,
                    self.test_dataloader,
                    self.keys_dict[client_id]
                )
            self.client_pool.append(new_client)
    
    def run(self):
        self._regist_client()
        self._construct_dataloader()
        self._construct_sign()

        # 初始化激励池（在构建客户端之前）
        if self.enable_pol and self.pol_config.get('enable_blockchain', False) and self.pol_config.get('enable_incentives', False):
            self._initialize_incentive_pool()
            # 清理之前实验的区块链记录，避免混淆
            self._reset_blockchain_state()

        self._construct_client()
        
        for i in range(self.global_args['communication_round']):
            logger.info(f"开始第 {i+1}/{self.global_args['communication_round']} 轮通信")

            # 更新监控进度
            if self.monitor:
                self.monitor.update_progress(self.experiment_id, i + 1)

            # 并行化客户端训练和测试（性能优化）
            self._parallel_client_training(i)

            # 异步批量提交PoL证明到区块链（真正的异步，不阻塞主流程）
            self._async_batch_submit_pol_proofs()

            # 接收上传并进行PoL验证
            verified_clients = self.server.receive_upload(self.client_pool)

            # 聚合模型
            global_model = self.server.aggregate()

            # 分发全局模型
            for client in self.client_pool:
                client.load_state_dict(global_model)

            # 如果是PoL聚合器，打印验证统计和区块链信息
            if hasattr(self.server, 'get_verification_stats'):
                stats = self.server.get_verification_stats()
                logger.info(f"第 {i+1} 轮PoL验证统计: {stats}")

                # 打印客户端区块链信誉信息
                if self.enable_pol and self.pol_config.get('enable_blockchain', False):
                    self._log_blockchain_stats()
                    self._log_incentive_pool_stats()

                self.server.reset_verification_stats()

                # 开始新一轮（区块链）
                if self.enable_pol and self.pol_config.get('enable_blockchain', False):
                    from chainfl.pol_blockchain_client import pol_blockchain_client
                    pol_blockchain_client.start_new_round()

        logger.info("Task Finished")

        # 计算最终准确率（从最后一轮的客户端测试结果）
        final_accuracy = 0.0
        if self.client_pool:
            # 取所有客户端的平均准确率
            total_acc = 0.0
            valid_clients = 0
            for client in self.client_pool:
                if hasattr(client, 'test_acc') and client.test_acc is not None:
                    total_acc += client.test_acc
                    valid_clients += 1

            if valid_clients > 0:
                final_accuracy = total_acc / valid_clients

        # 完成实验监控
        if self.monitor:
            metrics = ExperimentMetrics(
                accuracy=final_accuracy,
                communication_round=self.global_args.get('communication_round', 0)
            )
            self.monitor.update_progress(
                self.experiment_id,
                self.global_args.get('communication_round', 0),
                metrics
            )
            self.monitor.complete_experiment(self.experiment_id, success=True)

        # 返回实验结果
        return {
            'final_accuracy': final_accuracy,
            'convergence_rounds': self.global_args.get('communication_round', 0),
            'communication_cost': 0.0,
            'storage_cost': 0.0,
            'detection_rate': 0.0,
            'false_positive_rate': 0.0
        }

    def _async_batch_submit_pol_proofs(self):
        """异步批量提交PoL证明到区块链 - 真正的异步，不阻塞主流程"""
        if not self.pol_config.get('enable_blockchain', False):
            return

        # 收集所有需要提交的PoL证明
        submissions = []
        for client in self.client_pool:
            if hasattr(client, 'pol_proof') and client.pol_proof:
                submissions.append({
                    'client_address': client.client_address,
                    'pol_proof': client.pol_proof,
                    'ipfs_hash': None,
                    'client_id': client.client_id
                })

        if not submissions:
            logger.debug("没有PoL证明需要提交到区块链")
            return

        def async_blockchain_submit():
            """异步区块链提交任务"""
            try:
                import threading
                import time
                from chainfl.pol_blockchain_client import pol_blockchain_client

                logger.info(f"🚀 开始异步批量提交 {len(submissions)} 个PoL证明到区块链")

                # 使用带超时的提交
                results = self._submit_with_timeout(pol_blockchain_client, submissions, timeout=30)

                # 更新客户端的提交结果（线程安全）
                success_count = 0
                for i, submission in enumerate(submissions):
                    if i < len(results):
                        result = results[i]
                        # 查找对应的客户端
                        for client in self.client_pool:
                            if hasattr(client, 'client_id') and client.client_id == submission['client_id']:
                                if hasattr(client, 'pol_submission_result'):
                                    client.pol_submission_result = result
                                break

                        if result.success:
                            success_count += 1
                            logger.debug(f"客户端 {submission['client_id']} PoL证明异步提交成功")
                        else:
                            logger.warning(f"客户端 {submission['client_id']} PoL证明异步提交失败: {result.error_message}")

                logger.info(f"🎉 异步批量PoL证明提交完成：{success_count}/{len(submissions)} 成功")

            except Exception as e:
                logger.error(f"异步批量提交PoL证明时发生错误: {e}")
                logger.warning("区块链提交失败，但不影响训练流程继续")

        # 在独立的守护线程中执行区块链提交，不阻塞主流程
        import threading
        submit_thread = threading.Thread(target=async_blockchain_submit, daemon=True)
        submit_thread.start()
        logger.debug(f"异步区块链提交任务已启动，包含 {len(submissions)} 个PoL证明")

    def _submit_with_timeout(self, pol_blockchain_client, submissions, timeout=30):
        """带超时的区块链提交"""
        import threading
        import time

        results = []
        completed = threading.Event()

        def submit_task():
            try:
                nonlocal results
                results = pol_blockchain_client.batch_submit_pol_proofs_with_timeout(submissions, timeout)
                completed.set()
            except Exception as e:
                logger.error(f"超时提交任务异常: {e}")
                completed.set()

        submit_thread = threading.Thread(target=submit_task, daemon=True)
        submit_thread.start()

        # 等待完成或超时
        if completed.wait(timeout):
            return results
        else:
            logger.warning(f"区块链提交超时 ({timeout}秒)，放弃等待")
            return []

    def _parallel_client_training(self, epoch: int):
        """并行化客户端训练和测试 - 改进的容错版本"""
        logger.info(f"🚀 开始并行训练 {len(self.client_pool)} 个客户端（第{epoch+1}轮）")

        def train_single_client_with_timeout(client):
            """训练单个客户端的函数，带超时控制"""
            import time
            start_time = time.time()

            try:
                # 训练（移除了同步区块链提交，应该更快）
                client.train(epoch=epoch)

                # 测试
                test_result = client.test(epoch=epoch)
                if test_result and 'acc' in test_result:
                    client.test_acc = test_result['acc']

                # 签名测试
                client.sign_test(epoch=epoch)

                # 如果是PoL客户端，验证自己的证明（添加超时保护）
                if hasattr(client, 'verify_own_proof'):
                    try:
                        # 为PoL验证添加超时保护
                        import threading
                        import time

                        verification_result = [False]  # 使用列表以便在线程中修改
                        verification_completed = threading.Event()

                        def verify_with_timeout():
                            try:
                                result = client.verify_own_proof()
                                verification_result[0] = result
                            except Exception as e:
                                logger.warning(f"客户端 {client.client_id} PoL验证异常: {e}")
                                verification_result[0] = False
                            finally:
                                verification_completed.set()

                        verify_thread = threading.Thread(target=verify_with_timeout, daemon=True)
                        verify_thread.start()

                        # 等待验证完成或超时（20秒，适应不同环境）
                        if verification_completed.wait(timeout=20):
                            if not verification_result[0]:
                                logger.warning(f"客户端 {client.client_id} PoL自验证失败，但继续训练")
                        else:
                            logger.warning(f"客户端 {client.client_id} PoL验证超时，跳过验证继续训练")

                    except Exception as e:
                        logger.warning(f"客户端 {client.client_id} PoL验证过程异常: {e}，跳过验证继续训练")

                elapsed_time = time.time() - start_time
                logger.debug(f"客户端 {client.client_id} 训练完成，耗时 {elapsed_time:.2f}秒")
                return True

            except Exception as e:
                elapsed_time = time.time() - start_time
                logger.error(f"客户端 {client.client_id} 训练失败: {e}，耗时 {elapsed_time:.2f}秒")
                return False

        # 使用线程池并行执行客户端训练，添加总体超时控制
        max_workers = min(len(self.client_pool), 3)  # 减少并发数避免资源竞争（从4降到3）

        # 超时时间计算：按论文10-15%开销 + 30%缓冲
        enable_pol = self.global_args.get('enable_pol', False)
        if enable_pol:
            # 基础训练时间估算
            base_timeout = 180  # 3分钟基础训练时间
            # 论文PoL开销：10-15%，我们给30%缓冲
            pol_overhead_factor = 1.3  # 30%开销缓冲
            # 并行因子：考虑资源竞争
            parallel_factor = min(1.5, len(self.client_pool) / 4)
            # 额外缓冲时间
            buffer_time = 120  # 2分钟缓冲
            training_timeout = int(base_timeout * pol_overhead_factor * parallel_factor + buffer_time)
        else:
            # 普通训练：基础时间
            training_timeout = 300  # 5分钟

        logger.info(f"设置并行训练超时时间: {training_timeout}秒 (PoL: {enable_pol}, 设备: {self.device})")

        import time
        from concurrent.futures import TimeoutError

        start_time = time.time()

        try:
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有训练任务
                future_to_client = {
                    executor.submit(train_single_client_with_timeout, client): client
                    for client in self.client_pool
                }

                # 等待所有任务完成，但有超时限制
                completed_count = 0
                failed_count = 0

                try:
                    for future in as_completed(future_to_client, timeout=training_timeout):
                        client = future_to_client[future]
                        try:
                            success = future.result(timeout=30)  # 每个客户端最多等30秒
                            if success:
                                completed_count += 1
                            else:
                                failed_count += 1
                        except Exception as e:
                            logger.error(f"客户端 {client.client_id} 训练异常: {e}")
                            failed_count += 1

                except TimeoutError:
                    logger.warning(f"并行训练总体超时 ({training_timeout}秒)，强制继续")
                    # 统计未完成的任务
                    remaining_tasks = len(self.client_pool) - completed_count - failed_count
                    failed_count += remaining_tasks
                    logger.warning(f"有 {remaining_tasks} 个客户端因超时被标记为失败")

        except Exception as e:
            logger.error(f"并行训练过程中发生严重错误: {e}")
            # 确保有合理的统计数据
            if completed_count + failed_count == 0:
                failed_count = len(self.client_pool)

        elapsed_time = time.time() - start_time
        logger.info(f"🎉 并行训练完成：{completed_count}/{len(self.client_pool)} 成功，{failed_count} 失败，总耗时 {elapsed_time:.2f}秒")

        # 如果失败率过高，记录警告
        if failed_count > len(self.client_pool) * 0.5:
            logger.warning(f"⚠️ 训练失败率过高 ({failed_count}/{len(self.client_pool)})，请检查系统状态")

    def _initialize_incentive_pool(self):
        """初始化激励池"""
        try:
            from chainfl.pol_blockchain_client import pol_blockchain_client

            # 确保区块链客户端已初始化
            if not pol_blockchain_client._initialized:
                pol_blockchain_client._initialize_blockchain()

            initial_rewards = 10000
            pol_blockchain_client.add_to_incentive_pool(initial_rewards)
            logger.info(f"激励池初始化完成，初始奖励: {initial_rewards}")
        except Exception as e:
            logger.error(f"激励池初始化失败: {e}")

    def _reset_blockchain_state(self):
        """重置区块链状态，避免不同实验间的记录混淆"""
        try:
            from chainfl.pol_blockchain_client import pol_blockchain_client

            # 重置客户端账户映射，确保每次实验都是干净的状态
            if hasattr(pol_blockchain_client, '_client_account_mapping'):
                logger.info(f"重置客户端账户映射，之前有 {len(pol_blockchain_client._client_account_mapping)} 个客户端")
                pol_blockchain_client._client_account_mapping.clear()
                pol_blockchain_client._next_account_index = 1

            # 开始新一轮，这会重置一些状态
            pol_blockchain_client.start_new_round()
            logger.info("区块链状态已重置，开始新实验")

        except Exception as e:
            logger.warning(f"重置区块链状态失败: {e}")

    def _log_blockchain_stats(self):
        """记录区块链统计信息"""
        try:
            from chainfl.pol_blockchain_client import pol_blockchain_client

            logger.info("=" * 50)
            logger.info("区块链统计信息:")

            pol_record_counts = []  # 用于检查PoL记录一致性

            for client in self.client_pool:
                if hasattr(client, 'client_address'):
                    # 修复：使用实际的区块链地址查询信誉
                    if hasattr(client, 'actual_blockchain_address') and client.actual_blockchain_address:
                        query_address = client.actual_blockchain_address
                    else:
                        # 如果没有实际地址，通过客户端地址获取对应的区块链账户地址
                        client_account = pol_blockchain_client._get_client_account(client.client_address)
                        query_address = client_account.address

                    reputation = pol_blockchain_client.get_client_reputation(query_address)
                    pol_records = pol_blockchain_client.get_pol_records(query_address)
                    pol_record_counts.append(len(pol_records))

                    logger.info(f"客户端 {client.client_id} (区块链地址: {query_address[-8:]}):")
                    logger.info(f"  - 信誉分数: {reputation.reputation_score}")
                    logger.info(f"  - 提交次数: {reputation.total_submissions}")
                    logger.info(f"  - 有效次数: {reputation.valid_submissions}")
                    logger.info(f"  - 成功率: {reputation.valid_submissions/max(1,reputation.total_submissions):.2%}")
                    logger.info(f"  - 是否拉黑: {reputation.is_blacklisted}")
                    logger.info(f"  - PoL记录数量: {len(pol_records)}")

            # 检查PoL记录一致性
            if pol_record_counts:
                min_records = min(pol_record_counts)
                max_records = max(pol_record_counts)
                avg_records = sum(pol_record_counts) / len(pol_record_counts)

                logger.info("=" * 50)
                logger.info("PoL记录一致性检查:")
                logger.info(f"  - 最少记录数: {min_records}")
                logger.info(f"  - 最多记录数: {max_records}")
                logger.info(f"  - 平均记录数: {avg_records:.1f}")

                if max_records - min_records > 0:
                    logger.warning(f"⚠️ PoL记录数量不一致！差异: {max_records - min_records}")
                    logger.warning("   - 可能原因：区块链提交失败、网络问题、nonce冲突")
                    logger.warning("   - 建议：检查网络连接和区块链节点状态")
                else:
                    logger.info("✅ 所有客户端的PoL记录数量一致")

            logger.info("=" * 50)

        except Exception as e:
            logger.error(f"记录区块链统计信息失败: {e}")

    def _log_incentive_pool_stats(self):
        """记录激励池统计信息"""
        try:
            from chainfl.pol_blockchain_client import pol_blockchain_client

            pool_info = pol_blockchain_client.get_incentive_pool_info()
            logger.info("激励池状态:")
            logger.info(f"  - 总奖励: {pool_info['total_rewards']}")
            logger.info(f"  - 已分配: {pool_info['distributed_rewards']}")
            logger.info(f"  - 剩余: {pool_info['total_rewards'] - pool_info['distributed_rewards']}")
            logger.info(f"  - 当前轮次: {pool_info['current_round']}")

        except Exception as e:
            logger.error(f"记录激励池统计信息失败: {e}")

    def _detect_device(self):
        """自动检测GPU/CPU设备"""
        if torch.cuda.is_available():
            device_count = torch.cuda.device_count()
            device_name = torch.cuda.get_device_name(0)
            logger.info(f"🚀 检测到 {device_count} 个GPU设备: {device_name}")
            device = 'cuda'
        else:
            logger.info("🔧 未检测到GPU，使用CPU设备")
            device = 'cpu'

        logger.info(f"✅ 设备设置: {device}")
        return device