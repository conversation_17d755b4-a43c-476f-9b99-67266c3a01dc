import logging
from .algorithm import *
from util.device_manager import auto_configure_device, device_manager
#The list of support choice
#


logger = logging.getLogger(__name__)

model = ["simpleCNN",
         "SignAlexNet",
         "resnet18",
         "resnet34",
         "resnet50",
         "resnet101",
         "resnet152",
         "VGG_A",
         "VGG_B",
         "VGG_D",
         "VGG_E",]

optimizer = ["SGD",
             "Adam"]

class BenchMark:
    def __init__(self, name):
        logger.info("Initializing Benchmark %s", name)
        self.global_args = None
        self.train_args  = None
        self.algorithm   = None
    def get_args(self):
        # 自动配置设备
        if self.train_args:
            self.train_args = auto_configure_device(self.train_args)
        return self.global_args, self.train_args, self.algorithm

class FashionMNIST(BenchMark):
    def __init__(self):
        super(FashionMNIST,self).__init__('FashionMNIST')
        self.global_args = {
            'client_num': 10,
            'model': 'simpleCNN',
            'dataset': 'FashionMNIST',
            'batch_size': 32,
            'class_num': 10,
            'data_folder': './experiments/data',
            'communication_round': 200,
            'non-iid': False,
            'alpha': 1,
        }
        self.train_args = {
            'optimizer': 'SGD',
            'device': 'cuda',
            'lr': 1e-2,
            'weight_decay': 1e-5,
            'num_steps': 1,
        }
        self.algorithm = FedAvg()
        
class CIFAR10(BenchMark):
    def __init__(self):
        super(CIFAR10,self).__init__('CIFAR10')
        self.global_args = {
            'client_num': 10,
            'model': 'resnet18',
            'dataset': 'CIFAR10',
            'batch_size': 32,
            'class_num': 10,
            'data_folder': './experiments/data',
            'communication_round': 200,
            'non-iid': False,
            'alpha': 1,
        }
        self.train_args = {
            'optimizer': 'SGD',
            'device': 'cuda',
            'lr': 1e-2,
            'weight_decay': 1e-5,
            'num_steps': 1,
        }
        self.algorithm = FedAvg()
        
class Sign(BenchMark):
    def __init__(self):
        super(Sign,self).__init__('Sign')
        self.global_args = {
            'client_num': 10,
            'sign_num' : 10,
            'model': 'SignAlexNet',
            'sign_config': {'0': False, '2': False, '4': 'signature', '5': 'signature', '6': 'signature'},
            'bit_length' : 40,
            'dataset': 'CIFAR10',
            'in_channels': 3,
            'batch_size': 32,
            'class_num': 10,
            'data_folder': './experiments/data',
            'communication_round': 200,
            'non-iid': False,
            'alpha': 1,
            'sign' : True,
        }
        self.train_args = {
            'optimizer': 'SGD',
            'device': 'cuda',
            'lr': 1e-2,
            'weight_decay': 1e-5,
            'num_steps': 1,
        }
        self.algorithm = FedIPR()
        
        
class PoLFashionMNIST(BenchMark):
    """支持PoL的FashionMNIST基准测试"""
    def __init__(self):
        super(PoLFashionMNIST, self).__init__('PoLFashionMNIST')
        self.global_args = {
            'client_num': 10,
            'model': 'simpleCNN',
            'dataset': 'FashionMNIST',
            'batch_size': 32,
            'class_num': 10,
            'data_folder': './experiments/data',
            'communication_round': 50,  # 减少轮数以便测试
            'non-iid': False,
            'alpha': 1,
            # PoL相关配置
            'enable_pol': True,
            'pol_save_freq': None,  # 自动设置为每epoch一次（按PoL论文建议）
            'pol_verification_ratio': 0.2,  # 验证20%的检查点
            'require_pol': True,
            'enable_compression': True,  # 启用增量检查点压缩
            # 区块链相关配置
            'enable_blockchain': True,
            'enable_incentives': True,
        }
        self.train_args = {
            'optimizer': 'SGD',
            'device': 'auto',  # 自动检测GPU/CPU
            'lr': 1e-2,
            'weight_decay': 1e-5,
            'num_steps': 1,
        }
        self.algorithm = PoLFedAvg()

class PoLCIFAR10(BenchMark):
    """支持PoL的CIFAR10基准测试"""
    def __init__(self):
        super(PoLCIFAR10, self).__init__('PoLCIFAR10')
        self.global_args = {
            'client_num': 10,
            'model': 'resnet18',
            'dataset': 'CIFAR10',
            'batch_size': 32,
            'class_num': 10,
            'data_folder': './experiments/data',
            'communication_round': 50,
            'non-iid': False,
            'alpha': 1,
            # PoL相关配置
            'enable_pol': True,
            'pol_save_freq': 100,
            'pol_verification_ratio': 0.1,
            'require_pol': True,
            'enable_compression': True,  # 启用增量检查点压缩
            # 区块链相关配置
            'enable_blockchain': True,
            'enable_incentives': True,
        }
        self.train_args = {
            'optimizer': 'SGD',
            'device': 'auto',  # 自动检测GPU/CPU
            'lr': 1e-2,
            'weight_decay': 1e-5,
            'num_steps': 1,
        }
        self.algorithm = PoLFedAvg()

class CIFAR100(BenchMark):
    """CIFAR100基准测试"""
    def __init__(self):
        super(CIFAR100, self).__init__('CIFAR100')
        self.name = 'CIFAR100'  # 显式设置name属性
        self.global_args = {
            'client_num': 10,
            'model': 'resnet34',  # 使用更大的模型处理100类
            'dataset': 'CIFAR100',
            'batch_size': 32,
            'class_num': 100,
            'data_folder': './experiments/data',
            'communication_round': 200,
            'non-iid': False,
            'alpha': 1,
        }
        self.train_args = {
            'optimizer': 'SGD',
            'device': 'cuda',
            'lr': 1e-2,
            'weight_decay': 1e-4,  # 稍微增加正则化
            'num_steps': 1,
        }
        self.algorithm = FedAvg()

class PoLCIFAR100(BenchMark):
    """支持PoL的CIFAR100基准测试"""
    def __init__(self):
        super(PoLCIFAR100, self).__init__('PoLCIFAR100')
        self.name = 'PoLCIFAR100'  # 显式设置name属性
        self.global_args = {
            'client_num': 10,
            'model': 'resnet34',  # 使用更大的模型处理100类
            'dataset': 'CIFAR100',
            'batch_size': 32,
            'class_num': 100,
            'data_folder': './experiments/data',
            'communication_round': 50,
            'non-iid': False,
            'alpha': 1,
            # PoL相关配置
            'enable_pol': True,
            'pol_save_freq': 150,  # 由于模型更大，减少保存频率
            'pol_verification_ratio': 0.05,  # 减少验证比例以节省时间
            'require_pol': True,
            'enable_compression': True,
            # 区块链相关配置
            'enable_blockchain': True,
            'enable_incentives': True,
        }
        self.train_args = {
            'optimizer': 'SGD',
            'device': 'auto',  # 自动检测GPU/CPU
            'lr': 1e-2,
            'weight_decay': 1e-4,
            'num_steps': 1,
        }
        self.algorithm = PoLFedAvg()

def get_benchmark(args: str) -> BenchMark:
    if(args == "FashionMNIST"):
        return FashionMNIST()
    elif (args == "CIFAR10"):
        return CIFAR10()
    elif (args == "CIFAR100"):
        return CIFAR100()
    elif(args == "Sign"):
        return Sign()
    elif(args == "PoLFashionMNIST"):
        return PoLFashionMNIST()
    elif(args == "PoLCIFAR10"):
        return PoLCIFAR10()
    elif(args == "PoLCIFAR100"):
        return PoLCIFAR100()
    else:
        logger.error(f"Unknown Benchmark {args}")
        raise Exception(f"Unknown Benchmark {args}")
