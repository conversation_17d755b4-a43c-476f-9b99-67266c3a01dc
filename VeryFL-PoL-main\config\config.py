"""
通用配置类
为实验脚本提供灵活的配置容器
"""

import logging
from typing import Any, Dict, Optional

logger = logging.getLogger(__name__)

class Config:
    """
    通用配置类
    支持动态属性设置，用于实验配置管理
    """
    
    def __init__(self, **kwargs):
        """
        初始化配置
        
        Args:
            **kwargs: 初始配置参数
        """
        # 默认配置
        self._defaults = {
            # 基础配置
            'client_num': 10,
            'communication_round': 50,
            'batch_size': 32,
            'learning_rate': 0.01,
            'local_epochs': 1,
            'device': 'cpu',
            
            # 数据集配置
            'dataset': 'FashionMNIST',
            'model': 'simpleCNN',
            'class_num': 10,
            'data_folder': './experiments/data',  # 修复：数据集保存到experiments目录下
            'non_iid': False,
            'alpha': 1.0,
            
            # PoL配置
            'enable_pol': False,
            'pol_save_freq': None,  # 自动设置为每epoch一次（按PoL论文建议）
            'pol_verification_ratio': 0.1,
            'pol_compression_ratio': 0.1,
            'require_pol': False,
            
            # 压缩配置
            'enable_compression': False,
            'compression_type': 'quantization',
            'quantization_bits': 8,
            
            # 区块链配置
            'enable_blockchain': False,
            'enable_incentives': False,
            
            # 攻击配置
            'malicious_ratio': 0.0,
            'attack_type': 'none',
            
            # 优化器配置
            'optimizer': 'SGD',
            'weight_decay': 1e-5,
            'momentum': 0.9,
            
            # 其他配置
            'output_dir': './experiments/results',
            'save_model': False,
            'verbose': True,
        }
        
        # 设置默认值
        for key, value in self._defaults.items():
            setattr(self, key, value)
        
        # 设置传入的参数
        for key, value in kwargs.items():
            setattr(self, key, value)
    
    def __setattr__(self, name: str, value: Any):
        """动态设置属性"""
        super().__setattr__(name, value)
    
    def __getattr__(self, name: str) -> Any:
        """动态获取属性"""
        if name.startswith('_'):
            raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")
        return None
    
    def update(self, **kwargs):
        """
        批量更新配置
        
        Args:
            **kwargs: 要更新的配置参数
        """
        for key, value in kwargs.items():
            setattr(self, key, value)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典
        
        Returns:
            配置字典
        """
        result = {}
        for key, value in self.__dict__.items():
            if not key.startswith('_'):
                result[key] = value
        return result
    
    def from_dict(self, config_dict: Dict[str, Any]):
        """
        从字典加载配置
        
        Args:
            config_dict: 配置字典
        """
        for key, value in config_dict.items():
            setattr(self, key, value)
    
    def get_global_args(self) -> Dict[str, Any]:
        """
        获取全局参数（用于Task类）
        
        Returns:
            全局参数字典
        """
        return {
            'client_num': self.client_num,
            'model': self.model,
            'dataset': self.dataset,
            'batch_size': self.batch_size,
            'class_num': self.class_num,
            'data_folder': self.data_folder,
            'communication_round': self.communication_round,
            'non-iid': self.non_iid,
            'alpha': self.alpha,
            # PoL相关配置
            'enable_pol': self.enable_pol,
            'pol_save_freq': self.pol_save_freq,
            'pol_verification_ratio': self.pol_verification_ratio,
            'require_pol': self.require_pol,
            'enable_compression': self.enable_compression,
            'enable_blockchain': self.enable_blockchain,
            'enable_incentives': self.enable_incentives,
            # 攻击相关配置
            'malicious_ratio': self.malicious_ratio,
            'attack_type': self.attack_type,
        }
    
    def get_train_args(self) -> Dict[str, Any]:
        """
        获取训练参数（用于Task类）
        
        Returns:
            训练参数字典
        """
        return {
            'optimizer': self.optimizer,
            'device': self.device,
            'lr': self.learning_rate,
            'weight_decay': self.weight_decay,
            'num_steps': self.local_epochs,
        }
    
    def __repr__(self) -> str:
        """字符串表示"""
        config_items = []
        for key, value in self.to_dict().items():
            config_items.append(f"{key}={value}")
        return f"Config({', '.join(config_items)})"
    
    def __str__(self) -> str:
        """友好的字符串表示"""
        lines = ["Configuration:"]
        for key, value in sorted(self.to_dict().items()):
            lines.append(f"  {key}: {value}")
        return "\n".join(lines)


class ExperimentConfig(Config):
    """
    实验专用配置类
    继承自Config，添加实验特定的功能
    """
    
    def __init__(self, name: str = "experiment", **kwargs):
        """
        初始化实验配置
        
        Args:
            name: 实验名称
            **kwargs: 其他配置参数
        """
        super().__init__(**kwargs)
        self.name = name
        self.experiment_id = None
        self.start_time = None
        self.end_time = None
    
    def get_experiment_info(self) -> Dict[str, Any]:
        """
        获取实验信息
        
        Returns:
            实验信息字典
        """
        return {
            'name': self.name,
            'experiment_id': self.experiment_id,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'config': self.to_dict()
        }


# 便捷函数
def create_config(**kwargs) -> Config:
    """
    创建配置实例的便捷函数
    
    Args:
        **kwargs: 配置参数
        
    Returns:
        Config实例
    """
    return Config(**kwargs)


def create_experiment_config(name: str, **kwargs) -> ExperimentConfig:
    """
    创建实验配置实例的便捷函数
    
    Args:
        name: 实验名称
        **kwargs: 配置参数
        
    Returns:
        ExperimentConfig实例
    """
    return ExperimentConfig(name=name, **kwargs)
