from torchvision import datasets, transforms
from config.dataset import dataset_file_path
import os
import time

def get_fashionmnist(train: bool = True):
    """获取FashionMNIST数据集，带重试机制"""
    max_retries = 3
    retry_delay = 5  # 秒

    for attempt in range(max_retries):
        try:
            # 检查数据集是否已存在
            data_path = os.path.join(dataset_file_path, 'FashionMNIST')
            if os.path.exists(data_path) and len(os.listdir(data_path)) > 0:
                # 数据集已存在，不需要下载
                download = False
            else:
                download = True

            return datasets.FashionMNIST(
                root=dataset_file_path,
                train=train,
                download=download,
                transform=transforms.Compose([
                    transforms.RandomCrop(28, padding=4),
                    transforms.RandomHorizontalFlip(),
                    transforms.ToTensor(),
                ])
            )
        except Exception as e:
            print(f"FashionMNIST下载尝试 {attempt + 1}/{max_retries} 失败: {e}")
            if attempt < max_retries - 1:
                print(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
            else:
                # 最后一次尝试失败，使用简化的transform
                print("使用简化的数据集配置...")
                try:
                    return datasets.FashionMNIST(
                        root=dataset_file_path,
                        train=train,
                        download=True,
                        transform=transforms.Compose([
                            transforms.ToTensor(),
                        ])
                    )
                except Exception as final_e:
                    print(f"最终失败: {final_e}")
                    raise RuntimeError(f"无法加载FashionMNIST数据集: {final_e}")
    