
--- 第 1 页 ---
JOURNALOFLATEXCLASSFILES,VOL.14,NO.8,AUGUST2015 1
VeryFL: A Verify Federated Learning Framework
Embedded with Block<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Member, IEEE, and <PERSON><PERSON><PERSON>, Fellow, IEEE
Abstract—Blockchain-empowered federated learning (FL) has Currently, the open-source framework of federated learning
provoked extensive research recently. Various blockchain-based is mainly based on centralized client-server paradigm. For
federated learning algorithm, architecture and mechanism have
example,FedMLisalarge-scalefederatedlearningframework
been designed to solve issues like single point failure and data
thatcanbedeployedtoserveasafederatedlearninginfrastruc-
falsification brought by centralized FL paradigm. Moreover, it
is easier to allocate incentives to nodes with the help of the tureinproductionenvironment.Andforexperimentalpropose,
blockchain. Various centralized federated learning frameworks therearemanyframeworkswhichcontainsalotofbenchmark
like FedML, have emerged in the community to help boost to help reproduce the FL experiment such as EasyFL.
the research on FL. However, decentralized blockchain-based However, after investigating the existing blockchain-based
federated learning framework is still missing, which cause in-
FLframeworkonGithub,wefoundthatthereisstillalackof
convenience for researcher to reproduce or verify the algorithm
performance based on blockchain. Inspired by the above issues, theblockchain-basedFLframeworkthatprovidesaconvenient
we have designed and developed a blockchain-based federated experimental environment. Existing frameworks are either a
learning framework by embedding Ethereum network. This demo to design a specific algorithm or lack of the basic
reportwillpresenttheoverallstructureofthisframework,which benchmark of FL to execute experiments compared with the
proposes a code practice paradigm for the combination of FL
centralizedFLframeworks.Forexample,FedCoinbuildsap2p
with blockchain and, at the same time, compatible with normal
FL training task. In addition to implement some blockchain payment system for FL, but it lacks the basic FL algorithm
federatedlearningalgorithmsonsmartcontracttohelpexecutea and dataset to run other experiment on it. Based on the above
FL training, we also propose a model ownership authentication investigation, we find that it is necessary to build a unified
architecture based on blockchain and model watermarking to federated learning framework embedded with blockchain to
protect the intellectual property rights of models. These mecha-
providetherelativeexperimentwithanexecutionenvironment. nism on blockchain shows an underlying support of blockchain
Next,wewillintroduceouropen-sourceframeworkVeryFL
forfederatedlearningtoprovideaverifiabletraining,aggregation
and incentive distribution procedure and thus we named this based on PyTorch and Ethereum. Different from existing
framework VeryFL (A Verify Federated Learninig Framework federated learning frameworks, VeryFL extends the support
Embedded with Blockchain) . The source code is avaliable on for algorithm concerned with blockchain federated learning
https://github.com/GTMLLab/VeryFL
and have three main features. (1) VeryFL comes with the
Index Terms—Federated Learning, Model Ownership, basic federated learninig algorithm and benchmark dataset to
Blockchain, Framework
execute FL experiment like other centralized FL framework.
(2)VeryFLembeddedwiththeEthereumnetworkwhichisin-
I. INTRODUCTION teracted through the python SDK. Many on-chain mechanism
FEDERATED learning is a distributed machine learning can be ealisy implemented with smart contract and verified in
paradigm that allows all participants to collaboratively real blochchain environment. (3) VeryFL designs algorithm to
train machine learning models from multiple data sources providemodelownershipverificationservicestoprotectmodel
without disclosing private data. Federated learning offers a rights through blockchain. Smart contracts are introduced to
solution to challenges associated with data silos and privacy assist in model authentication and are responsible for the
preservation, presenting a wide spectrum of potential applica- distribution, management and identification of model tokens.
tions. In recent years, federated learning has been a popular Wesummarizethecharacteristicsoftheexistingmainstream
research field in machine learning, with numerous significant open-source frameworks alongside the VeryFL framework in
works focusing on heterogeneity [1], [2], [3], communication Table I.
efficiency [4], [5], privacy protection [6], [7] and other diffi- The characteristics of VeryFL are delineated as follows:
cult problems. However, federated learning under centralized • Usability: VeryFL offers benchmark datasets and aggre-
serverstillfacetheriskofsinglepointfailure,datafalsification gation algorithms for federated learning, requiring mini-
and lack of incentives. Moreover, federated learning model mal configurationin the absence of specializeddemands,
which is trained by multiple parties is exposed to risks such thereby presenting a novice-friendly environment.
as model illegal copying, misuse and free-riding[8], and thus • Scalability: Owing to its modular design, VeryFL is
need a mechanism to protect intellectual property rights. To readilyextensible,facilitatinguserstocustomizedatasets,
face both of the risks brought by centralized paradigm and aggregation algorithms, and other components pertinent
model rights, blockchain has become a novel solutions for FL to their specific tasks.
to solve these challenges and build a more robust and secure • Blockchain Embedded: We embedded Ethereum for
execution environment. user to write and invoke on-chain mechanism through
3202
voN
72
]GL.sc[
1v71651.1132:viXra

--- 第 2 页 ---
JOURNALOFLATEXCLASSFILES,VOL.14,NO.8,AUGUST2015 2
TABLEI
COMPARISONWITHEXISTINGFEDERATEDLEARNINGOPEN-SOURCEFRAMEWORKS
Blockchain FL-side Model
FL Benchmark
Embedded Architecture Authentication
EasyFL [9] (cid:33) - (cid:33) -
FedML [10] (cid:33) - (cid:33) -
GFL [11] - (cid:33) - -
FedCoin [12] - (cid:33) - -
blocklearning [13] - (cid:33) - -
FLoBc [14] - (cid:33) - -
VeryFL (cid:33) (cid:33) (cid:33) (cid:33)
smart contract. Beyond providing foundational support
for federated learning, VeryFL also affords a prototype
system for model ownership verification, thereby broad-
ening the scope of federated learning applications.
• Model Rights Protection: We implement our Tokenized
Model[15] on VeryFL. With the watermark embedded
in the model, we implement a framework where the
blockchain turns the model into a non-fungible to-
ken(NFT) for the first time to better protect the model
rights and allow model to be transacted.
In the subsequent sections, we will introduce VeryFL in
detail. Section 2 will introduce the architecture and various
technologies utilized in VeryFL. Section 3 will introduce the
novel attributes of VeryFL. Section 4 will provide practical
Fig.1. FrameworkofVeryFL.
instances of using VeryFL. Finally, Section 5 will be reserved
for summarization and prospective outlooks.
designed to facilitate smart contracts and decentralized appli-
cations. Solidity is a programming language tailored specifi-
II. FRAMEWORK
callyforEthereumsmartcontracts,whileBrownieisaPython-
In this section, we will introduce the overall framework baseddevelopmentframeworkforthedevelopmentandtesting
of VeryFL constructed upon PyTorch and Ethereum. The ofEthereumsmartcontracts.Thesecomponentstogetherform
frameworkisshowninFig.1.WiththehelpofBrownieSDK, a comprehensive ecosystem.
theblockchain(smartcontracts)providesPythonAPItogether In VeryFL, blochchain module acts as an manager of the
with PyTorch at the foundational layer. Atop the Python API, training network. If a user wishes to participate in federated
we encapsulate the federated learning process into a Task learning training, it is first required to apply for an accounts
class to perform FL training and we wrap the blockchain API id for user management. For model copyright protection,
into ChainProxy class to deal the interaction with the smart blockchain will distribute watermark for each unique model,
contract. Therefore, the architecture can be roughly divided serving as an identifier for model copyright. This token is
into two principal modules: the blockchain component and bound to the model and is recorded in the blockchain, speci-
the federated learning task component. fying the user to whom it belongs. And for training manage-
ment, client and server will upload per-round training result
including accuarcy, loss and dataset size to the blockchain
A. Blockchain for client selection and incentive distribution. Through this
bottom blockchain module, many on-chain mechanism can be
Blockchain often acts as a de-trusted ledger among FL
implemented on the Ethereum network.
training nodes to provide a unchangeable record of the FL
training process. Therefore, the blockchain module is mainly
B. Federate Learning Task
responsible for the management and election of the client,
recording of the training process, and distribution of the In common blockchain-FL, all node has the equal oppor-
incentives. In the blockchain module, smart contracts are tunity to perform aggregation and no specific defined server.
executed on the Ethereum blockchain network using Solidity, However, to keep the compatibility with the centralized FL,
subsequently interacting with PyTorch via the Python SDK, VeryFL still defines the Aggregator class to perform the
Brownie. Ethereum is an programmable blockchain platform, aggregation task. With the election of the blockchain during

--- 第 3 页 ---
JOURNALOFLATEXCLASSFILES,VOL.14,NO.8,AUGUST2015 3
eachtrainiground,wehandlethisaggregatortodifferentclient more trustworthy. Furthermore, through this process, VeryFL
and simulate the decentralized aggregation process. establish a model-user-watermark mapping, where the model
The main entry of the FL task is in Task object. It encap- trained by the user itself is recorded on the chain as an
sulates necessary components for a federated learning process on-chain asset similar to NFT through the watermark as a
and controls the training logic where the FL training process representative. In this way, emulating the nature of NFT, we
starts. With the passed-in-parameters, Task will initialized canimplementmarketablebehaviorssuchastradingandown-
the component like models, datasets and federated learning ership verification against deep learning models. A detailed
algorithms. VeryFL provides some benchmarks dataset and descriptionoftheaboveprocessisdocumentedinourprevious
models and some embedded FL algorithm, facilitating a rapid papers where we propose the tokenized model[15].
construction of a federated learning workflow. In addition to
benchmarks provided by VeryFL by default, users can easily IV. CODEPRACTICE
customize these components according to their individual
A. Modulized Design
requirements by inheriting from the corresponding abstract
In order to facilitate the subsequent expansion of the
classes and rewriting the core code of algorithm.
federated learning algorithm, most parts of the framework
have adopted modular design. Decoupling each parts of the
III. FEATURES
federated learning makes it possible for VeryFL to contains
A. Federated Learning Framework different FL algorithm. With the predefined benchmark’s
Similar with all other federated learning frameworks, the configure file as the entry point which is passed into Task
framework comes with several common federated learning object, the server, the model, the dataset, the client and the
benchmarks and image classification datasets in order to trainer, are instantiated as a single object and assembled
make it easy for users to quickly start a federated learning together for the training of federated learning. The advantage
experiment.Commonfederatedlearningexperimentsareoften of implementing each part as a module is that framework can
conducted on the CIFAR10, CIFAR100, and FashionMnist reuse the same parts of the federated learning algorithm as
datasets, and the framework provides pre-configurations in much as possible. For instance, FedAvg and FedProx only
the Benchmark module to get start and running quickly. differ in client training, the same Aggregator module can be
In addition to the basic configuration and modules, some reusedonbothalgorithms.Whileinothercases,implementing
utils classes are designed to personalize the experiment. For a federated learning algorithm requires implementing both
example, DatasetSpliter is provided at the dataset partition Aggregator and Trainer. By adopting modular design, we can
to support federated learning experiments with Non-IID data easily extend new FL algorithm into VeryFL.
distribution.
B. Task Start
B. On-chain Mechanism with Smart Contract To facilitate a quick start of a task, by importing the
predefined training parameters and passing it into Task, we
With the help of the programmable Ethereum, VeryFL
can quickly start a federated learning training task.
can implement the on-chain mechanism or algorithm through
smart contract. With the help of brownie python API, VeryFL1 from task import Task
can call these predefined smart contract during training pro-2 import config.benchmark
3 global args , train args , algorithm = benchmark.
cess. Many research on blockchain-FL focus on building
get args()
reliable, private and secure FL system. This feature provide4 classification task = Task(global args=global args ,
us with the convenience to test our on-chain FL algorithm in train args=train args , algorithm=algorithm)
5 classification task.run()
a real blockchain network. VeryFL implement some example
smartcontracttoexecutetheclientregistration,clientelection VeryFL provides benchmark for FL tasks. In Benchmark
and training result record functions. class, VeryFL devided the arguments of the FL training
into global args, train args and Algorithm. The global args
C. Model Ownership verification with Blockchain controls the overall setting of a FL task like model, datasets
and number of clients. The train args controls the traing
BeyondtheFLtrainingfunctionsofVeryFL,wealsobuilda
argumentsinlocalmodeltraininglikelearningrate,optimizer
modelrightsprotectionandtransactionplatformonVaryFLas
and weight decay. By changing the parameters in Benchmark,
an example of blockchain empowered federated learning. The
VeryFL can start a customized training.
basic algorithm references the model watermarking techology
representedbyFedIPR[8].TheproposalofcombiningFedIPR
C. Blockchain Interface
with blockchain model watermarking algorithm is based on
the need for model ownership authentication, where by em- Since blockchain development is almost inevitably accom-
bedding specific watermarking information, the trainer can paniedbythewritingofSolidity,VeryFLprovidesChainProxy
claim ownership of the model. Different with the FedIPR as the interface to interact with the blockchain. Users can de-
scenario,theframeworkhandsoverthedistribution,recording ploy smart contracts written by themselves and write wrapper
and authentication process of watermarks to smart contracts functions in ChainProxy to wrap the smart contracts interface
to manage, making the distribution process of watermarks into python functions that can work together with PyTorch.

--- 第 4 页 ---
JOURNALOFLATEXCLASSFILES,VOL.14,NO.8,AUGUST2015 4
1) Start Ethereum Network: The following code line 1 [8] B.Li,L.Fan,H.Gu,J.Li,andQ.Yang,“Fedipr:Ownershipverification
shows the process of starting the blockchain network, which for federated deep neural network models,” IEEE Transactions on
Pattern Analysis and Machine Intelligence, vol. 45, no. 4, pp. 4521–
start an instance of a blockchain network. After starting the
4536,2022.
network successfully, VeryFL begins to deploy the smart [9] W. Zhuang, X. Gan, Y. Wen, and S. Zhang, “Easyfl: A low-code
contracts. federated learning platform for dummies,” IEEE Internet of Things
Journal,2022.
2) Deployment of Smart Contracts: At the begining, the
[10] C. He, S. Li, J. So, X. Zeng, M. Zhang, H. Wang, X. Wang,
blockchain will have ten accounts whose addresses is stored P. Vepakomma, A. Singh, H. Qiu et al., “Fedml: A research li-
in a list. We use accounts[0] as the server accounts to deploy brary and benchmark for federated machine learning,” arXiv preprint
arXiv:2007.13518,2020.
smart contracts. The deployment procedure is shown in line
[11] “Gfl,”https://github.com/GalaxyLearning/GFL.
4, 5. The server accounts After deployment, VeryFL can call [12] Y.Liu,Z.Ai,S.Sun,S.Zhang,Z.Liu,andH.Yu,“Fedcoin:Apeer-
functions through the object of a smart contract instance. to-peerpaymentsystemforfederatedlearning,”inFederatedlearning:
privacyandincentive. Springer,2020,pp.125–138.
3) Predefined Smart Contract: VeryFL provides some pre-
[13] “blocklearning,”https://github.com/hacdias/blocklearning.
defined smart contract which have already been wrapped [14] M.Ghanem,F.Dawoud,H.Gamal,E.Soliman,andT.El-Batt,“Flobc:
in ChainProxy. They provides the registration function that A decentralized blockchain-based federated learning framework,” in
2022 Fourth International Conference on Blockchain Computing and
corresponds the client to the blockchain user’s address, the
Applications(BCCA). IEEE,2022,pp.85–92.
training result recording function and the model watermark [15] Y. Li, Y. Lai, T. Liao, C. Chen, and Z. Zheng, “Tokenized model:
function mentioned in Chapter 3. These processes can be A blockchain-empowered decentralized model ownership verification
platform,”arXivpreprintarXiv,2023.
called to supervise and manage the FL training.
1 from brownie.project.chainServer import *
2 network.connect(’development’)
3 server accounts = accounts[0]
4 watermarkNegotiation.deploy({’from’:server accounts
})
5 clientManager.deploy({’from’:server accounts})
V. FUTUREWORK
This report present an open-source framework VeryFL. In
this framework, we design an extensive and flexible workflow
forFL.Besides,wealsoembedblockchainintothisframework
tohelpboosttheresearchofblockchain-empoweredfederated
learning. With the help of embedded Ethereum network, we
design a prototype system for model ownership verification
mechanism and the incentive mechanism as an example of
combining blockchain with FL. We will reproduce more
blockchain-basedalgorithmonthisplatformtohelpuserverify
these algorithm conveniently.
REFERENCES
[1] T.Li,A.K.Sahu,M.Zaheer,M.Sanjabi,A.Talwalkar,andV.Smith,
“Federated optimization in heterogeneous networks,” Proceedings of
Machinelearningandsystems,vol.2,pp.429–450,2020.
[2] S. P. Karimireddy, S. Kale, M. Mohri, S. Reddi, S. Stich, and A. T.
Suresh, “Scaffold: Stochastic controlled averaging for federated learn-
ing,”inInternationalconferenceonmachinelearning. PMLR,2020,
pp.5132–5143.
[3] Q. Li, B. He, and D. Song, “Model-contrastive federated learning,”
in Proceedings of the IEEE/CVF conference on computer vision and
patternrecognition,2021,pp.10713–10722.
[4] D. Rothchild, A. Panda, E. Ullah, N. Ivkin, I. Stoica, V. Braverman,
J. Gonzalez, and R. Arora, “Fetchsgd: Communication-efficient feder-
atedlearningwithsketching,”inInternationalConferenceonMachine
Learning. PMLR,2020,pp.8253–8265.
[5] H.-P. Wang, S. Stich, Y. He, and M. Fritz, “Progfed: effective, com-
munication,andcomputationefficientfederatedlearningbyprogressive
training,” in International Conference on Machine Learning. PMLR,
2022,pp.23034–23054.
[6] J.Zhao,H.Zhu,F.Wang,R.Lu,Z.Liu,andH.Li,“Pvd-fl:Aprivacy-
preserving and verifiable decentralized federated learning framework,”
IEEETransactionsonInformationForensicsandSecurity,vol.17,pp.
2059–2073,2022.
[7] L. Sun, J. Qian, and X. Chen, “Ldp-fl: Practical private aggregation
in federated learning with local differential privacy,” arXiv preprint
arXiv:2007.15789,2020.
