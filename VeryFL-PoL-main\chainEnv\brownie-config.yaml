project_structure:
    build: build
    contracts: contracts
    interfaces: interfaces
    reports: reports
    scripts: scripts
    tests: tests

dependencies:
    - OpenZeppelin/openzeppelin-contracts@4.8.0

compiler:
    solc:
        version: 0.8.19
        optimizer:
            enabled: true
            runs: 200

networks:
    development:
        host: http://127.0.0.1:8545
        gas_limit: 12000000
        gas_price: 20000000000
        chainid: 1337
        timeout: 300
        # 添加更多配置以提高稳定性
        reverting_tx_gas_limit: 12000000
        max_fee_per_gas: 30000000000
        max_priority_fee_per_gas: 2000000000
        # 交易确认配置
        required_confs: 1
        # 连接重试配置
        request_timeout: 120  # 增加到120秒，避免PoL提交超时
        retry_count: 5        # 增加重试次数

reports:
    exclude_contracts:
        - SafeMath
