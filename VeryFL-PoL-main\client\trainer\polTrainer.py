"""
支持PoL的训练器
在训练过程中生成学习证明
"""

import copy
import logging
import torch
from torch import nn
from client.base.baseTrainer import BaseTrainer
from pol.pol_generator import PoLGenerator

logger = logging.getLogger(__name__)

class PoLTrainer(BaseTrainer):
    """支持学习证明生成的训练器"""
    
    def __init__(self, model, dataloader, criterion, args={}, watermarks={}, 
                 pol_config=None):
        """
        初始化PoL训练器
        
        Args:
            model: 神经网络模型
            dataloader: 数据加载器
            criterion: 损失函数
            args: 训练参数
            watermarks: 水印参数（继承自原始框架）
            pol_config: PoL配置参数
        """
        super().__init__(model, dataloader, criterion, args, watermarks)
        self.criterion = torch.nn.CrossEntropyLoss()
        
        # PoL配置
        self.pol_config = pol_config or {}
        self.enable_pol = self.pol_config.get('enable_pol', True)

        # 严格按照PoL论文：k=S（每epoch保存一次）
        # 论文第919-921行明确指出：k=S是基线设置，存储开销为1×
        self.pol_save_freq = None  # 将在start_training时设置为steps_per_epoch
        self.pol_save_dir = self.pol_config.get('save_dir', './experiments/pol_data')

        # 初始化PoL生成器
        if self.enable_pol:
            self.pol_generator = PoLGenerator(
                save_freq=self.pol_save_freq,  # None值，将在start_training时自动设置
                save_dir=self.pol_save_dir,
                enable_compression=self.pol_config.get('enable_compression', True)
            )
            logger.info("PoL训练器初始化完成，已启用学习证明生成")
        else:
            self.pol_generator = None
            logger.info("PoL训练器初始化完成，未启用学习证明生成")
    
    def train(self, total_epoch, client_id=None):
        """
        重写训练方法，集成PoL生成
        
        Args:
            total_epoch: 训练轮数
            client_id: 客户端ID
            
        Returns:
            训练结果列表和PoL证明
        """
        self.construct_optimizer()
        
        # 启动PoL生成
        if self.enable_pol and self.pol_generator:
            if client_id is None:
                client_id = f"client_{id(self)}"  # 使用对象ID作为默认客户端ID

            # 计算每个epoch的步数（基于PoL论文建议）
            steps_per_epoch = len(self.dataloader)
            logger.info(f"计算得到每epoch步数: {steps_per_epoch}")

            self.pol_generator.start_training(self.model, client_id, steps_per_epoch)
        
        ret_list = []
        batch_count = 0
        
        for epoch in range(self.start_epoch, total_epoch):
            epoch_result = self._train_epoch_with_pol(epoch, batch_count)
            ret_list.append(epoch_result)
            batch_count += len(self.dataloader)
        
        # 生成最终的PoL证明
        pol_proof = None
        if self.enable_pol and self.pol_generator:
            pol_proof = self.pol_generator.finalize_proof(self.model)
        
        return ret_list, pol_proof
    
    def _train_epoch_with_pol(self, epoch, batch_offset=0):
        """
        支持PoL的单轮训练
        
        Args:
            epoch: 当前轮数
            batch_offset: 批次偏移量
            
        Returns:
            训练结果字典
        """
        model = self.model
        args = self.args
        device = args.get("device", "cpu")
        
        model.to(device)
        model.train()
        
        batch_loss = []
        
        for batch_idx, (x, labels) in enumerate(self.dataloader):
            x, labels = x.to(device), labels.to(device)
            
            # 记录批次信息到PoL
            if self.enable_pol and self.pol_generator:
                self.pol_generator.record_batch(x, labels, batch_idx, epoch)
            
            # 前向传播
            log_probs = model(x)
            loss = self.criterion(log_probs, labels)
            
            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            self.optimizer.step()
            
            batch_loss.append(loss.item())
            
            # 记录模型更新到PoL
            if self.enable_pol and self.pol_generator:
                self.pol_generator.record_model_update(
                    model, self.optimizer, loss.item(), epoch, batch_idx
                )
        
        # 计算平均损失
        if len(batch_loss) == 0:
            epoch_loss = 0.0
        else:
            epoch_loss = sum(batch_loss) / len(batch_loss)
        
        ret = {
            'loss': epoch_loss,
            'epoch': epoch,
            'batch_count': len(batch_loss)
        }
        
        # 添加PoL相关信息
        if self.enable_pol and self.pol_generator:
            pol_summary = self.pol_generator.get_proof_summary()
            ret['pol_summary'] = pol_summary
        
        return ret
    
    def get_pol_proof(self):
        """获取当前的PoL证明"""
        if self.enable_pol and self.pol_generator:
            return self.pol_generator.finalize_proof(self.model)
        return None
    
    def _on_before_upload(self, epoch):
        """上传前的处理"""
        pass
    
    def _on_after_download(self, epoch):
        """下载后的处理"""
        pass
    
    def _upload_model(self, epoch):
        """上传模型（继承自基类）"""
        uploaded_para = {
            'epoch': epoch,
            'state_dict': self.model.state_dict(),
            'client_id': getattr(self, 'client_id', 'unknown')
        }
        
        # 如果启用了PoL，添加PoL证明摘要
        if self.enable_pol and self.pol_generator:
            pol_summary = self.pol_generator.get_proof_summary()
            uploaded_para['pol_summary'] = pol_summary
        
        self.pipe.upload_model(uploaded_para)
    
    def _download_model(self, epoch):
        """下载模型（继承自基类）"""
        download_params = self.pipe.download_model()
        self.model.load_state_dict(download_params['state_dict'])


class PoLFedProxTrainer(PoLTrainer):
    """支持PoL的FedProx训练器"""
    
    def __init__(self, model, dataloader, criterion, args={}, watermarks={}, 
                 pol_config=None, mu=0.5):
        super().__init__(model, dataloader, criterion, args, watermarks, pol_config)
        self.mu = mu  # FedProx的近端项系数
        
    def _train_epoch_with_pol(self, epoch, batch_offset=0):
        """
        支持PoL的FedProx单轮训练
        """
        model = self.model
        args = self.args
        device = args.get("device", "cpu")
        
        model.to(device)
        model.train()
        
        # 保存全局模型参数用于FedProx近端项
        global_model_params = copy.deepcopy(model.state_dict())
        
        batch_loss = []
        
        for batch_idx, (x, labels) in enumerate(self.dataloader):
            x, labels = x.to(device), labels.to(device)
            
            # 记录批次信息到PoL
            if self.enable_pol and self.pol_generator:
                self.pol_generator.record_batch(x, labels, batch_idx, epoch)
            
            # 前向传播
            log_probs = model(x)
            loss = self.criterion(log_probs, labels)
            
            # 添加FedProx近端项
            fed_prox_reg = 0.0
            for name, param in model.named_parameters():
                fed_prox_reg += ((self.mu / 2) * 
                               torch.norm((param - global_model_params[name].data.to(device))) ** 2)
            loss += fed_prox_reg
            
            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            self.optimizer.step()
            
            batch_loss.append(loss.item())
            
            # 记录模型更新到PoL
            if self.enable_pol and self.pol_generator:
                self.pol_generator.record_model_update(
                    model, self.optimizer, loss.item(), epoch, batch_idx
                )
        
        # 计算平均损失
        if len(batch_loss) == 0:
            epoch_loss = 0.0
        else:
            epoch_loss = sum(batch_loss) / len(batch_loss)
        
        ret = {
            'loss': epoch_loss,
            'epoch': epoch,
            'batch_count': len(batch_loss),
            'fed_prox_mu': self.mu
        }
        
        # 添加PoL相关信息
        if self.enable_pol and self.pol_generator:
            pol_summary = self.pol_generator.get_proof_summary()
            ret['pol_summary'] = pol_summary
        
        return ret
